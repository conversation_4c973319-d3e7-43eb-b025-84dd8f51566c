/* pages/payment/payment.wxss */
.container {
  padding: 40rpx 30rpx;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  color: #333;
}

.subtitle {
  font-size: 30rpx;
  color: #666;
}

.usage-info {
  width: 100%;
  margin-bottom: 40rpx;
}

.usage-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  display: block;
}

.usage-progress {
  width: 100%;
}

.packages-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 40rpx;
}

.package-item {
  background-color: #fff;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.package-item.selected {
  border-color: #4A90E2;
  background-color: #f0f7ff;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.package-name {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.package-check {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #4A90E2;
  display: flex;
  justify-content: center;
  align-items: center;
}

.check-icon {
  width: 24rpx;
  height: 24rpx;
}

.package-price {
  margin-bottom: 15rpx;
}

.price-currency {
  font-size: 30rpx;
  color: #4A90E2;
  font-weight: bold;
}

.price-amount {
  font-size: 48rpx;
  color: #4A90E2;
  font-weight: bold;
}

.package-description {
  font-size: 26rpx;
  color: #666;
}

.benefits-container {
  width: 100%;
  background-color: #fff;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.benefits-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.benefit-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.benefit-text {
  font-size: 28rpx;
  color: #666;
}

.payment-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.payment-notice {
  text-align: center;
}

.notice-text {
  font-size: 26rpx;
  color: #999;
}

.notice-link {
  font-size: 26rpx;
  color: #4A90E2;
}
