// pages/index/index.js
const app = getApp()

Page({
  data: {
    isLoggedIn: false,
    userInfo: null,
    canUseApp: true,
    usageCount: 0,
    maxFreeUsage: 5
  },

  onLoad: function () {
    this.checkLoginStatus();
    this.checkUsageLimit();
  },

  onShow: function () {
    this.checkLoginStatus();
    this.checkUsageLimit();
  },

  checkLoginStatus: function () {
    const isLoggedIn = app.globalData.isLoggedIn;
    const userInfo = app.globalData.userInfo;
    
    this.setData({
      isLoggedIn,
      userInfo
    });

    if (!isLoggedIn) {
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login',
        });
      }, 2000); // Show the landing page for 2 seconds before redirecting
    }
  },

  checkUsageLimit: function () {
    const canUseApp = app.checkUsageLimit();
    const usageCount = app.globalData.usageCount;
    const maxFreeUsage = app.globalData.maxFreeUsage;
    
    this.setData({
      canUseApp,
      usageCount,
      maxFreeUsage
    });
  },

  goToSolver: function () {
    if (!this.data.isLoggedIn) {
      wx.navigateTo({
        url: '/pages/login/login',
      });
      return;
    }

    if (!this.data.canUseApp) {
      wx.navigateTo({
        url: '/pages/payment/payment',
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/solver/solver',
    });
  },

  goToPayment: function () {
    wx.navigateTo({
      url: '/pages/payment/payment',
    });
  }
})
