# 数学学习助手微信小程序

这是一款帮助小学、初中、高中生学习数学的微信小程序，利用腾讯OCR云函数进行题目识别，并使用DeepSeek-v3-0324模型构建的云函数实现苏格拉底提问法，一步步从审题开始帮助学生解决数学问题。

## 功能特点

1. **智能题目识别**：支持拍照或上传图片识别数学题目
2. **苏格拉底式教学**：通过引导性提问帮助学生一步步思考解题过程
3. **多种辅助功能**：提供提示、例题和相关知识点支持
4. **免费试用**：提供5次免费使用机会，之后需要付费升级
5. **个性化学习**：根据学生年级、地区和教材版本提供定制化辅导

## 项目结构

```
├── app.js                 // 小程序入口文件
├── app.json               // 小程序全局配置
├── app.wxss               // 小程序全局样式
├── pages                  // 页面文件夹
│   ├── index              // 首页
│   ├── login              // 登录页
│   ├── solver             // 解题页
│   ├── payment            // 支付页
│   └── user               // 用户页
├── cloudfunctions         // 云函数
│   ├── tencentOCR         // 腾讯OCR云函数
│   └── deepseek           // DeepSeek模型云函数
└── images                 // 图片资源
```

## 环境配置

### 1. 微信开发者工具设置

- 下载并安装[微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
- 创建新项目，选择"小程序云开发"模板
- 在项目设置中启用"云开发"功能

### 2. 云开发环境配置

- 创建云开发环境
- 在云开发控制台中创建以下环境变量：

#### 腾讯OCR云函数环境变量
```
SecretId: AKIDSlqiNn13rRr30lIgtvjJelfOuLYXHn46
SecretKey: rlXXP3VlnbBZljTyGMEsKacT6XrxpUv7
```

#### DeepSeek云函数环境变量
```
LKEAP_API_KEY: sk-PU9ZaxwyoAqFZdsqVNF6CMe7Z9CMhIGe0MCaC7EDFo5Ebkez
LKEAP_ENDPOINT: //api.lkeap.cloud.tencent.com
LKEAP_PATH: /v1/chat/completions
LKEAP_MODEL: deepseek-v3-0324
```

### 3. 部署云函数

在微信开发者工具中，右键点击cloudfunctions目录下的每个云函数文件夹，选择"上传并部署：云端安装依赖"。

## 使用流程

1. **首次使用**：用户需要登录并提供基本信息（地区、年级、教材版本）
2. **上传题目**：通过拍照或从相册选择图片上传数学题目
3. **确认题目**：确认OCR识别的题目内容是否正确
4. **解题过程**：系统会通过苏格拉底提问法引导学生一步步思考解题过程
5. **辅助功能**：在解题过程中，学生可以随时获取提示、查看类似例题或相关知识点

## 开发注意事项

1. 请确保在微信开发者工具中正确配置AppID
2. 云函数部署前需要先安装依赖
3. 小程序需要申请相机和相册权限
4. 支付功能需要商户资质，本示例仅作演示

## 资源文件

项目需要以下图片资源（需自行准备）：
- logo.png - 应用logo
- camera.png - 相机图标
- upload.png - 上传图标
- ai.png - AI图标
- step.png - 步骤图标
- avatar.png - 用户头像
- avatar_placeholder.png - 默认头像
- check.png - 勾选图标
- vip.png - VIP图标
- support.png - 客服图标
- about.png - 关于图标
- arrow.png - 箭头图标
- logout.png - 退出图标
- unlimited.png - 无限图标
- hint.png - 提示图标
- priority.png - 优先图标
- home.png/home_selected.png - 首页图标
- user.png/user_selected.png - 用户图标

## 许可证

[MIT](LICENSE)
