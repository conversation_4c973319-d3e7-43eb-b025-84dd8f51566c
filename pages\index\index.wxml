<!--pages/index/index.wxml-->
<view class="container">
  <view class="logo-container">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
  </view>
  
  <view class="slogan">
    <text class="slogan-text">搞定数学真的很简单</text>
  </view>
  
  <view class="card intro-card">
    <text class="intro-text">欢迎使用数学学习助手，我们将利用人工智能技术，帮助您一步步解决数学问题，提高学习效率。</text>
  </view>
  
  <view class="usage-info" wx:if="{{isLoggedIn && !canUseApp}}">
    <text class="usage-text text-warning">您已使用完免费次数 ({{usageCount}}/{{maxFreeUsage}})</text>
    <text class="usage-subtext">升级为付费用户，享受无限使用权限</text>
    <button class="btn-primary" bindtap="goToPayment">立即升级</button>
  </view>
  
  <view class="usage-info" wx:elif="{{isLoggedIn}}">
    <text class="usage-text">已使用 {{usageCount}}/{{maxFreeUsage}} 次免费服务</text>
  </view>
  
  <view class="action-container">
    <button class="btn-primary start-btn" bindtap="goToSolver">开始解题</button>
  </view>
  
  <view class="features-container">
    <view class="feature-item">
      <image class="feature-icon" src="/images/camera.png" mode="aspectFit"></image>
      <text class="feature-text">拍照识别题目</text>
    </view>
    <view class="feature-item">
      <image class="feature-icon" src="/images/upload.png" mode="aspectFit"></image>
      <text class="feature-text">上传题目图片</text>
    </view>
    <view class="feature-item">
      <image class="feature-icon" src="/images/ai.png" mode="aspectFit"></image>
      <text class="feature-text">AI智能解析</text>
    </view>
    <view class="feature-item">
      <image class="feature-icon" src="/images/step.png" mode="aspectFit"></image>
      <text class="feature-text">步骤详解</text>
    </view>
  </view>
</view>
