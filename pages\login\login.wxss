/* pages/login/login.wxss */
.container {
  padding: 40rpx 30rpx;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.logo {
  width: 150rpx;
  height: 150rpx;
  margin-bottom: 30rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.subtitle {
  font-size: 32rpx;
  color: #666;
}

.form-container {
  width: 100%;
  background-color: #fff;
  border-radius: 15rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  color: #333;
  background-color: #f9f9f9;
}

.picker {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  color: #333;
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 34rpx;
  font-weight: bold;
  margin-top: 20rpx;
}

.privacy-notice {
  margin-top: 40rpx;
  text-align: center;
}

.privacy-text {
  font-size: 26rpx;
  color: #999;
}

.privacy-link {
  font-size: 26rpx;
  color: #4A90E2;
  margin: 0 5rpx;
}
