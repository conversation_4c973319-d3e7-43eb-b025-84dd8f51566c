// 云函数入口文件
const cloud = require('wx-server-sdk')
const axios = require('axios')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, problem, conversation, userInfo } = event
  
  try {
    // 根据不同的action执行不同的操作
    switch (action) {
      case 'analyze':
        return await analyzeProblem(problem, userInfo)
      case 'question':
        return await generateQuestion(problem, conversation, userInfo)
      case 'hint':
        return await generateHint(problem, conversation, userInfo)
      case 'example':
        return await generateExample(problem, conversation, userInfo)
      case 'knowledge':
        return await generateKnowledge(problem, conversation, userInfo)
      default:
        throw new Error('未知的操作类型')
    }
  } catch (error) {
    console.error(error)
    return {
      success: false,
      error: error.message,
    }
  }
}

// 分析题目
async function analyzeProblem(problem, userInfo) {
  const prompt = `
你是一位专业的数学老师，现在需要帮助${userInfo.grade}的学生分析以下数学题目。
请分析这道题目的关键点，并用HTML标签标记重点内容（使用<strong>标签）。

题目：${problem}

请提供：
1. 题目类型
2. 涉及的数学概念
3. 解题需要的关键知识点
4. 用HTML格式返回带有重点标记的题目文本
`

  const response = await callDeepSeekAPI(prompt)
  
  // 解析返回的内容
  const content = response.choices[0].message.content
  
  // 提取HTML格式的题目
  const highlightedTextMatch = content.match(/<strong>.*?<\/strong>|<[^>]*>.*?<\/[^>]*>/g)
  const highlightedText = highlightedTextMatch ? highlightedTextMatch.join(' ') : problem
  
  return {
    success: true,
    analysisResult: content,
    highlightedText: highlightedText
  }
}

// 生成苏格拉底式提问
async function generateQuestion(problem, conversation, userInfo) {
  // 构建提示词
  let prompt = `
你是一位专业的${userInfo.grade}数学老师，使用苏格拉底式提问法引导学生一步步解决数学问题。
你的目标是通过提问引导学生思考，而不是直接给出答案。每次只提出一个问题，等待学生回答后再继续。
提问要符合学生的认知水平，给予适当的鼓励和引导。

题目：${problem}

请根据以下对话历史，提出下一个引导性问题：
`

  // 添加对话历史
  for (const msg of conversation) {
    prompt += `\n${msg.role === 'assistant' ? '老师' : '学生'}：${msg.content}`
  }
  
  // 判断是否是最后一步
  const isLastStep = shouldFinishSolution(conversation)
  
  if (isLastStep) {
    prompt += `\n请总结这道题的解题过程，给予学生鼓励，并提出2道类似的练习题让学生尝试解答。`
  }

  const response = await callDeepSeekAPI(prompt)
  
  return {
    success: true,
    question: response.choices[0].message.content,
    isSolved: isLastStep
  }
}

// 生成提示
async function generateHint(problem, conversation, userInfo) {
  const prompt = `
你是一位专业的${userInfo.grade}数学老师，现在需要为学生提供一个有用的提示，帮助他们解决当前遇到的困难，但不要直接给出完整解答。

题目：${problem}

当前解题进度：
${conversation.map(msg => `${msg.role === 'assistant' ? '老师' : '学生'}：${msg.content}`).join('\n')}

请提供一个简短、有针对性的提示，帮助学生突破当前困境：
`

  const response = await callDeepSeekAPI(prompt)
  
  return {
    success: true,
    hint: response.choices[0].message.content
  }
}

// 生成例题
async function generateExample(problem, conversation, userInfo) {
  const prompt = `
你是一位专业的${userInfo.grade}数学老师，现在需要为学生提供一个与当前题目类似但更简单的例题，并给出详细的解题步骤。

题目：${problem}

请提供：
1. 一道与当前题目类似但更简单的例题
2. 该例题的详细解题步骤
3. 解题思路说明
`

  const response = await callDeepSeekAPI(prompt)
  
  return {
    success: true,
    example: response.choices[0].message.content
  }
}

// 生成相关知识点
async function generateKnowledge(problem, conversation, userInfo) {
  const prompt = `
你是一位专业的${userInfo.grade}数学老师，现在需要为学生提供与当前题目相关的核心知识点解释。

题目：${problem}

请提供：
1. 这道题目涉及的核心数学概念
2. 这些概念的简明解释
3. 如何应用这些概念解决类似问题的方法
4. 可能的常见错误和避免方法
`

  const response = await callDeepSeekAPI(prompt)
  
  return {
    success: true,
    knowledge: response.choices[0].message.content
  }
}

// 调用DeepSeek API
async function callDeepSeekAPI(prompt) {
  const apiKey = process.env.LKEAP_API_KEY
  const endpoint = process.env.LKEAP_ENDPOINT
  const path = process.env.LKEAP_PATH
  const model = process.env.LKEAP_MODEL
  
  const url = `${endpoint}${path}`
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`
  }
  
  const data = {
    model: model,
    messages: [
      {
        role: 'user',
        content: prompt
      }
    ],
    temperature: 0.7,
    max_tokens: 2000
  }
  
  try {
    const response = await axios.post(url, data, { headers })
    return response.data
  } catch (error) {
    console.error('API调用失败:', error.response ? error.response.data : error.message)
    throw new Error('DeepSeek API调用失败')
  }
}

// 判断是否应该结束解题过程
function shouldFinishSolution(conversation) {
  // 如果对话次数超过10轮，考虑结束
  if (conversation.length > 20) {
    return true
  }
  
  // 检查最近的对话是否表明问题已解决
  const recentMessages = conversation.slice(-4)
  const combinedText = recentMessages.map(msg => msg.content).join(' ').toLowerCase()
  
  // 检查是否包含表示完成的关键词
  const completionKeywords = ['解答完成', '答案是', '最终结果', '解题完毕', '题目已解决', '正确答案']
  return completionKeywords.some(keyword => combinedText.includes(keyword))
}
