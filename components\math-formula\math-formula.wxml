<!--components/math-formula/math-formula.wxml-->
<view class="math-formula-container">
  <view wx:if="{{isLoading}}" class="loading">加载中...</view>
  <view wx:elif="{{errorMessage}}" class="error-message">{{errorMessage}}</view>
  <block wx:elif="{{article && (article.children || article.child)}}">
    <!-- 使用towxml渲染内容 -->
    <towxml nodes="{{article}}" />
  </block>
  <block wx:elif="{{isApplicationProblem}}">
    <view class="application-problem">
      <view wx:if="{{problemText && options.length > 0}}" class="structured-problem">
        <view class="problem-text">{{problemText}}</view>
        <view class="options-container">
          <view class="option-item" wx:for="{{options}}" wx:key="index">
            <text class="option-label">{{item.label}}</text>
            <image
              wx:if="{{item.formulaImage}}"
              class="formula-image-inline"
              src="{{item.formulaImage}}"
              mode="widthFix"
              binderror="onFormulaImageError"
              data-fallback="{{item.text}}"
              data-index="{{index}}"
            />
            <text wx:else class="option-text">{{item.text}}</text>
          </view>
        </view>
      </view>
      <view wx:else class="simple-text">{{formattedText}}</view>
    </view>
  </block>
  <block wx:elif="{{imageUrl}}">
    <image
      class="formula-image"
      src="{{imageUrl}}"
      mode="widthFix"
      binderror="onImageError"
      bindload="onImageLoad"
    ></image>
  </block>
  <view wx:else class="empty-formula">{{formula || '暂无公式'}}</view>
</view>
