<!--components/math-formula/math-formula.wxml-->
<view class="math-formula-container">
  <view wx:if="{{isLoading}}" class="loading">加载中...</view>
  <view wx:elif="{{errorMessage}}" class="error-message">{{errorMessage}}</view>
  <block wx:elif="{{article && article.children}}">
    <!-- 使用towxml渲染内容 -->
    <towxml nodes="{{article}}" />
  </block>
  <block wx:elif="{{isApplicationProblem}}">
    <view class="application-problem">{{formattedText}}</view>
  </block>
  <block wx:elif="{{imageUrl}}">
    <image
      class="formula-image"
      src="{{imageUrl}}"
      mode="widthFix"
      binderror="onImageError"
      bindload="onImageLoad"
    ></image>
  </block>
  <view wx:else class="empty-formula">{{formula || '暂无公式'}}</view>
</view>
