<!--pages/payment/payment.wxml-->
<view class="container">
  <view class="header">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <text class="title">升级为付费用户</text>
    <text class="subtitle">解锁无限使用次数</text>
  </view>
  
  <view class="usage-info">
    <text class="usage-text">您已使用 {{usageCount}}/{{maxFreeUsage}} 次免费服务</text>
    <progress class="usage-progress" percent="{{usageCount / maxFreeUsage * 100}}" stroke-width="4" activeColor="#4A90E2" backgroundColor="#e0e0e0"></progress>
  </view>
  
  <view class="packages-container">
    <block wx:for="{{packages}}" wx:key="id">
      <view class="package-item {{item.selected ? 'selected' : ''}}" bindtap="selectPackage" data-id="{{item.id}}">
        <view class="package-header">
          <text class="package-name">{{item.name}}</text>
          <view class="package-check" wx:if="{{item.selected}}">
            <image class="check-icon" src="/images/check.png" mode="aspectFit"></image>
          </view>
        </view>
        <view class="package-price">
          <text class="price-currency">¥</text>
          <text class="price-amount">{{item.price}}</text>
        </view>
        <text class="package-description">{{item.description}}</text>
      </view>
    </block>
  </view>
  
  <view class="benefits-container">
    <text class="benefits-title">会员特权</text>
    <view class="benefit-item">
      <image class="benefit-icon" src="/images/unlimited.png" mode="aspectFit"></image>
      <text class="benefit-text">无限次使用解题功能</text>
    </view>
    <view class="benefit-item">
      <image class="benefit-icon" src="/images/hint.png" mode="aspectFit"></image>
      <text class="benefit-text">无限次获取提示、例题和知识点</text>
    </view>
    <view class="benefit-item">
      <image class="benefit-icon" src="/images/priority.png" mode="aspectFit"></image>
      <text class="benefit-text">优先使用服务器资源，响应更快</text>
    </view>
  </view>
  
  <button class="btn-primary payment-btn" bindtap="processPayment" disabled="{{isProcessing}}">
    {{isProcessing ? '处理中...' : '立即支付'}}
  </button>
  
  <view class="payment-notice">
    <text class="notice-text">点击立即支付，表示您同意</text>
    <text class="notice-link">《会员服务协议》</text>
  </view>
</view>
