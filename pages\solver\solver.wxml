<!--pages/solver/solver.wxml-->
<view class="container">
  <!-- Upload Problem Step -->
  <view class="step-container" wx:if="{{step === 'upload'}}">
    <view class="step-title">
      <text class="title">上传题目</text>
      <text class="subtitle">拍照或从相册选择题目图片</text>
    </view>

    <view class="upload-container">
      <button class="upload-btn" bindtap="chooseImage">
        <image class="upload-icon" src="/images/camera.png" mode="aspectFit"></image>
        <text class="upload-text">拍照/选择图片</text>
      </button>
    </view>
  </view>

  <!-- Confirm Problem Step -->
  <view class="step-container" wx:if="{{step === 'confirm'}}">
    <view class="step-title">
      <text class="title">确认题目</text>
      <text class="subtitle">请确认识别的题目是否正确</text>
    </view>

    <view class="problem-image-container" wx:if="{{problemImage}}">
      <image class="problem-image" src="{{problemImage}}" mode="aspectFit"></image>
    </view>

    <view class="problem-text-container" style="display: none;">
      <textarea class="problem-text" value="{{problemText}}" bindinput="editProblem" placeholder="请输入或修改题目内容"></textarea>
    </view>

    <view class="formula-preview-container" wx:if="{{problemText}}">
      <text class="preview-title">题目预览：</text>
      <view class="formula-preview" bindtap="showEditModal">
        <math-formula formula="{{problemText}}"></math-formula>
      </view>
      <view class="edit-hint">点击上方预览区域可编辑题目</view>
    </view>

    <view class="action-buttons">
      <button class="btn-secondary" bindtap="goBack">返回</button>
      <button class="btn-primary" bindtap="confirmProblem">确认</button>
    </view>
  </view>

  <!-- Loading State -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="thinking-dots">
      <view class="thinking-animation"></view>
      <view class="thinking-animation"></view>
      <view class="thinking-animation"></view>
    </view>
    <text class="loading-text">{{loadingText}}</text>
  </view>

  <!-- Solve Problem Step -->
  <view class="step-container" wx:if="{{step === 'solve' && !isLoading}}">
    <view class="problem-display">
      <text class="problem-title">题目：</text>
      <view class="problem-content">
        <math-formula formula="{{problemText}}"></math-formula>
      </view>
    </view>

    <view class="conversation-container">
      <scroll-view class="conversation-scroll" scroll-y="true" scroll-into-view="{{conversation.length > 0 ? 'msg-' + (conversation.length - 1) : ''}}">
        <block wx:for="{{conversation}}" wx:key="index" wx:if="{{index > 0}}">
          <view class="message {{item.role === 'assistant' ? 'assistant' : 'user'}}" id="msg-{{index}}">
            <view class="message-content">{{item.content}}</view>
          </view>
        </block>

        <view class="thinking-container" wx:if="{{isThinking}}">
          <view class="thinking-dots">
            <view class="thinking-animation"></view>
            <view class="thinking-animation"></view>
            <view class="thinking-animation"></view>
          </view>
          <text class="thinking-text">思考中...</text>
        </view>
      </scroll-view>
    </view>

    <view class="input-container" wx:if="{{!isThinking && currentQuestion && step === 'solve'}}">
      <input class="answer-input" placeholder="请输入你的回答..." value="{{currentAnswer}}" bindconfirm="submitAnswer" confirm-type="send"/>
      <button class="send-btn" bindtap="submitAnswer">发送</button>
    </view>

    <view class="helper-buttons">
      <button class="helper-btn" bindtap="showHintContent">提示</button>
      <button class="helper-btn" bindtap="showExampleContent">例题</button>
      <button class="helper-btn" bindtap="showKnowledgeContent">相关知识点</button>
    </view>
  </view>

  <!-- Hint Modal -->
  <view class="modal" wx:if="{{showHint}}">
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">提示</text>
        <view class="modal-close" bindtap="closeHint">×</view>
      </view>
      <view class="modal-body">
        <text class="modal-text">{{hintContent}}</text>
      </view>
    </view>
  </view>

  <!-- Example Modal -->
  <view class="modal" wx:if="{{showExample}}">
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">例题</text>
        <view class="modal-close" bindtap="closeExample">×</view>
      </view>
      <view class="modal-body">
        <text class="modal-text">{{exampleContent}}</text>
      </view>
    </view>
  </view>

  <!-- Knowledge Modal -->
  <view class="modal" wx:if="{{showKnowledge}}">
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">相关知识点</text>
        <view class="modal-close" bindtap="closeKnowledge">×</view>
      </view>
      <view class="modal-body">
        <text class="modal-text">{{knowledgeContent}}</text>
      </view>
    </view>
  </view>
</view>
