// pages/payment/payment.js
const app = getApp()

Page({
  data: {
    packages: [
      { id: 'monthly', name: '月卡', price: 19.9, description: '30天内无限次使用', selected: true },
      { id: 'quarterly', name: '季卡', price: 49.9, description: '90天内无限次使用', selected: false },
      { id: 'yearly', name: '年卡', price: 168, description: '365天内无限次使用', selected: false }
    ],
    selectedPackage: 'monthly',
    isProcessing: false,
    usageCount: 0,
    maxFreeUsage: 5
  },

  onLoad: function (options) {
    this.setData({
      usageCount: app.globalData.usageCount,
      maxFreeUsage: app.globalData.maxFreeUsage
    });
  },

  selectPackage: function (e) {
    const packageId = e.currentTarget.dataset.id;
    
    // Update selected state
    const updatedPackages = this.data.packages.map(pkg => {
      return {
        ...pkg,
        selected: pkg.id === packageId
      };
    });
    
    this.setData({
      packages: updatedPackages,
      selectedPackage: packageId
    });
  },

  processPayment: function () {
    // Get selected package
    const selectedPackage = this.data.packages.find(pkg => pkg.id === this.data.selectedPackage);
    
    if (!selectedPackage) {
      wx.showToast({
        title: '请选择套餐',
        icon: 'none'
      });
      return;
    }

    this.setData({ isProcessing: true });

    // In a real app, you would call the WeChat Pay API here
    // For this demo, we'll simulate a successful payment
    setTimeout(() => {
      // Simulate payment success
      this.handlePaymentSuccess();
    }, 2000);
  },

  handlePaymentSuccess: function () {
    // Update user's premium status
    app.setPremiumStatus(true);
    
    wx.showToast({
      title: '支付成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        setTimeout(() => {
          this.setData({ isProcessing: false });
          wx.navigateBack();
        }, 2000);
      }
    });
  },

  handlePaymentFailure: function (errorMsg) {
    this.setData({ isProcessing: false });
    
    wx.showToast({
      title: errorMsg || '支付失败，请重试',
      icon: 'none',
      duration: 2000
    });
  },

  // In a real app, you would implement the actual WeChat Pay API
  // This is a simplified example for demonstration purposes
  callWeChatPay: function (params) {
    wx.requestPayment({
      timeStamp: params.timeStamp,
      nonceStr: params.nonceStr,
      package: params.package,
      signType: params.signType,
      paySign: params.paySign,
      success: res => {
        this.handlePaymentSuccess();
      },
      fail: err => {
        this.handlePaymentFailure(err.errMsg);
      }
    });
  }
})
