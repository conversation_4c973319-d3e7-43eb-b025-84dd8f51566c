// pages/login/login.js
const app = getApp()

Page({
  data: {
    regions: ['华北', '东北', '华东', '华中', '华南', '西南', '西北'],
    regionIndex: 0,
    grades: ['小学一年级', '小学二年级', '小学三年级', '小学四年级', '小学五年级', '小学六年级', 
             '初中一年级', '初中二年级', '初中三年级', 
             '高中一年级', '高中二年级', '高中三年级'],
    gradeIndex: 0,
    textbooks: ['人教版', '北师大版', '苏教版', '沪教版', '浙教版', '湘教版', '粤教版', '其他'],
    textbookIndex: 0,
    studentName: '',
    isSubmitting: false
  },

  onLoad: function (options) {
    // Check if user is already logged in
    if (app.globalData.isLoggedIn) {
      wx.switchTab({
        url: '/pages/index/index',
      })
    }
  },

  bindRegionChange: function(e) {
    this.setData({
      regionIndex: e.detail.value
    })
  },

  bindGradeChange: function(e) {
    this.setData({
      gradeIndex: e.detail.value
    })
  },

  bindTextbookChange: function(e) {
    this.setData({
      textbookIndex: e.detail.value
    })
  },

  inputStudentName: function(e) {
    this.setData({
      studentName: e.detail.value
    })
  },

  submitForm: function() {
    const { studentName, regions, regionIndex, grades, gradeIndex, textbooks, textbookIndex } = this.data;
    
    if (!studentName.trim()) {
      wx.showToast({
        title: '请输入学生姓名',
        icon: 'none'
      });
      return;
    }

    this.setData({ isSubmitting: true });

    // Create user info object
    const userInfo = {
      name: studentName,
      region: regions[regionIndex],
      grade: grades[gradeIndex],
      textbook: textbooks[textbookIndex],
      registrationDate: new Date().toISOString()
    };

    // Save to storage and global data
    wx.setStorageSync('userInfo', userInfo);
    app.globalData.userInfo = userInfo;
    app.globalData.isLoggedIn = true;

    // Show success message and redirect
    wx.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 1500,
      success: () => {
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index',
          });
          this.setData({ isSubmitting: false });
        }, 1500);
      }
    });
  }
})
