/**
 * KaTeX样式，专为微信小程序设计
 */

.katex-container {
  font-family: 'Times New Roman', Times, serif;
  font-size: 32rpx;
  line-height: 1.5;
  display: inline-block;
  white-space: nowrap;
  text-align: center;
  padding: 4rpx 0;
}

/* 分数样式 */
.katex-frac {
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  margin: 0 4rpx;
  position: relative;
}

.katex-num {
  display: block;
  padding: 0 4rpx 2rpx 4rpx;
  border-bottom: 1.5rpx solid #000;
  margin-bottom: 1rpx;
}

.katex-denom {
  display: block;
  padding: 2rpx 4rpx 0 4rpx;
}

/* 根号样式 */
.katex-sqrt {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  margin: 0 2rpx;
}

.katex-sqrt-sym {
  display: inline-block;
  vertical-align: top;
  margin-right: -2rpx;
  font-size: 110%;
}

.katex-sqrt-arg {
  display: inline-block;
  border-top: 1.5rpx solid #000;
  padding: 0 2rpx;
  margin-top: 4rpx;
}

/* 上标和下标样式 */
.katex-sup {
  display: inline-block;
  font-size: 70%;
  vertical-align: super;
  margin: 0 1rpx;
  line-height: 1;
}

.katex-sub {
  display: inline-block;
  font-size: 70%;
  vertical-align: sub;
  margin: 0 1rpx;
  line-height: 1;
}

/* 括号样式 */
.katex-bracket {
  display: inline-block;
  vertical-align: middle;
}

/* 矩阵样式 */
.katex-matrix {
  display: inline-table;
  vertical-align: middle;
  margin: 0 4rpx;
}

.katex-matrix-row {
  display: table-row;
}

.katex-matrix-cell {
  display: table-cell;
  text-align: center;
  padding: 2rpx 6rpx;
}

/* 其他常用样式 */
.katex-overline {
  border-top: 1rpx solid #000;
  display: inline-block;
}

.katex-underline {
  border-bottom: 1rpx solid #000;
  display: inline-block;
}

.katex-cancel {
  position: relative;
  display: inline-block;
}

.katex-cancel::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  border-top: 1.5rpx solid #000;
  transform: rotate(-5deg);
}
