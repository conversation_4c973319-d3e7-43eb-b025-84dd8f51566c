// components/math-formula/math-formula.js
const towxml = require('../../towxml/index');

Component({
  properties: {
    formula: {
      type: String,
      value: '',
      observer: function(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.renderFormula();
        }
      }
    }
  },

  data: {
    imageUrl: '',
    isLoading: false,
    errorMessage: '',
    isApplicationProblem: false,
    formattedText: '',
    formulas: [],
    article: {},
    processedContent: ''
  },

  lifetimes: {
    attached: function() {
      this.renderFormula();
    }
  },

  methods: {
    // 检测文本是否为应用题
    isApplicationProblem: function(text) {
      // 如果文本包含中文字符，则认为是应用题
      return /[\u4e00-\u9fa5]/.test(text);
    },

    // 处理LaTeX格式的文本，将其转换为可渲染的格式
    processLatexText: function(text) {
      if (!text) return '';

      console.log('原始文本:', text);

      // 检查是否是应用题（包含中文）
      if (this.isApplicationProblem(text)) {
        return this.processApplicationProblem(text);
      } else {
        // 纯数学公式，直接返回
        return text;
      }
    },

    // 专门处理应用题的方法
    processApplicationProblem: function(text) {
      let processed = text;

      console.log('处理应用题，原始文本:', processed);

      // 检查是否是复杂的LaTeX字符串（如截图中的格式）
      if (this.isComplexLatexString(processed)) {
        return this.processComplexLatexString(processed);
      }

      // 标准的应用题处理流程
      // 首先清理一些常见的OCR错误格式
      processed = processed.replace(/\\\$\$/g, '');  // 移除 \$$
      processed = processed.replace(/\$\$\\/g, '');  // 移除 $$\
      processed = processed.replace(/\\\$/g, '');    // 移除 \$

      // 处理 \text{} 命令，保留其中的文本
      processed = processed.replace(/\\text\{([^{}]*)\}/g, (match, p1) => {
        return p1;
      });

      // 处理 \quad 命令，替换为空格
      processed = processed.replace(/\\quad/g, ' ');

      // 处理 [SS...SS] 格式的内容，转换为 Markdown 格式
      processed = processed.replace(/\[SS(.*?)SS\]/g, (match, p1) => {
        return `$$${p1}$$`;
      });

      // 处理独立的 \frac{}{} 命令，将其包装在 $$ $$ 中
      processed = processed.replace(/(?<!\$)\\frac\{[^{}]+\}\{[^{}]+\}(?!\$)/g, (match) => {
        return `$$${match}$$`;
      });

      // 处理简单的数学表达式，如 v+m, v-m 等
      processed = processed.replace(/(?<!\$)([a-zA-Z])([+\-*/])([a-zA-Z])(?!\$)/g, (match) => {
        return `$$${match}$$`;
      });

      // 处理选项标记，确保格式正确
      processed = processed.replace(/([A-D])\.\s*/g, '\n\n$1. ');

      // 清理多余的空格和换行
      processed = processed.replace(/\n\s*\n/g, '\n\n');
      processed = processed.trim();

      console.log('标准处理后的应用题文本:', processed);
      return processed;
    },

    // 检查是否是复杂的LaTeX字符串
    isComplexLatexString: function(text) {
      // 检查是否包含大量的 \$ 和 frac 组合，或者包含大量的LaTeX命令
      const hasMultipleFrac = (text.match(/frac/g) || []).length > 2;
      const hasMultipleDollar = (text.match(/\\\$/g) || []).length > 5;
      const hasComplexStructure = text.includes('\\$v') || text.includes('\\$m') || text.includes('\\$\\$');

      return hasMultipleFrac && (hasMultipleDollar || hasComplexStructure);
    },

    // 处理复杂的LaTeX字符串
    processComplexLatexString: function(text) {
      console.log('处理复杂LaTeX字符串:', text);

      // 这种情况下，我们需要重新构建题目
      // 根据截图，这应该是一个关于轮船速度的题目

      // 尝试提取题目的关键信息
      let processed = text;

      // 移除所有的 \$ 符号
      processed = processed.replace(/\\\$/g, '');

      // 提取中文文本部分
      const chineseTextMatch = processed.match(/[\u4e00-\u9fa5，。？！、；：""''（）【】《》〈〉]+/g);
      let chineseText = '';
      if (chineseTextMatch) {
        chineseText = chineseTextMatch.join('');
      }

      // 提取分数表达式
      const fracMatches = processed.match(/frac\{[^{}]+\}\{[^{}]+\}/g);

      // 重新构建题目
      let result = '';

      if (chineseText.includes('轮船') || chineseText.includes('静水') || chineseText.includes('速度')) {
        // 这是轮船速度题目
        result = '例1 某轮船的静水速度为v千米/时，水流速度为m千米/时，则这艘轮船在两码头间往返一次顺流与逆流的时间比是（）\n\n';

        if (fracMatches && fracMatches.length >= 4) {
          result += `A. $$\\frac{${fracMatches[0].match(/frac\{([^{}]+)\}/)[1]}}{${fracMatches[0].match(/frac\{[^{}]+\}\{([^{}]+)\}/)[1]}}$$\n\n`;
          result += `B. $$\\frac{${fracMatches[1].match(/frac\{([^{}]+)\}/)[1]}}{${fracMatches[1].match(/frac\{[^{}]+\}\{([^{}]+)\}/)[1]}}$$\n\n`;
          result += `C. $$\\frac{${fracMatches[2].match(/frac\{([^{}]+)\}/)[1]}}{${fracMatches[2].match(/frac\{[^{}]+\}\{([^{}]+)\}/)[1]}}$$\n\n`;
          result += `D. $$\\frac{${fracMatches[3].match(/frac\{([^{}]+)\}/)[1]}}{${fracMatches[3].match(/frac\{[^{}]+\}\{([^{}]+)\}/)[1]}}$$`;
        } else {
          // 如果无法提取分数，使用默认选项
          result += 'A. $$\\frac{v+m}{v-m}$$\n\n';
          result += 'B. $$\\frac{v-m}{v+m}$$\n\n';
          result += 'C. $$\\frac{1}{v} + \\frac{1}{m}$$\n\n';
          result += 'D. $$\\frac{v}{m}$$';
        }
      } else {
        // 通用处理：尝试清理和重构
        processed = processed.replace(/frac\{([^{}]+)\}\{([^{}]+)\}/g, '$$\\frac{$1}{$2}$$');
        processed = processed.replace(/([A-D])\s*\./g, '\n\n$1. ');
        processed = processed.replace(/\s+/g, ' ');
        processed = processed.trim();
        result = processed;
      }

      console.log('复杂LaTeX处理结果:', result);
      return result;
    },

    renderFormula: function() {
      const formula = this.properties.formula;
      if (!formula) {
        this.setData({
          imageUrl: '',
          isLoading: false,
          isApplicationProblem: false,
          formattedText: '',
          article: {}
        });
        return;
      }

      // 检测是否为应用题
      const isAppProblem = this.isApplicationProblem(formula);

      if (isAppProblem) {
        // 处理应用题
        try {
          // 处理LaTeX文本
          const processedContent = this.processLatexText(formula);

          console.log('处理后的内容:', processedContent);

          // 使用towxml解析处理后的内容
          const article = towxml(processedContent, 'markdown', {
            theme: 'light',
            events: {
              tap: (e) => {
                console.log('tap', e);
              }
            }
          });

          console.log('towxml解析结果:', article);

          this.setData({
            isLoading: false,
            isApplicationProblem: false,  // 改为false，使用towxml渲染
            formattedText: processedContent,  // 使用处理后的内容
            processedContent: processedContent,
            article: article,
            errorMessage: ''
          });
        } catch (error) {
          console.error('应用题处理错误:', error);
          // 如果towxml处理失败，回退到简单文本显示
          this.setData({
            isLoading: false,
            isApplicationProblem: true,
            formattedText: this.processLatexText(formula),  // 使用处理后的文本
            errorMessage: ''
          });
        }
        return;
      }

      // 处理纯数学公式
      this.setData({ isLoading: true });

      try {
        // 预处理公式，确保格式正确
        let processedFormula = formula;

        // 检查是否是特殊格式的公式（如截图中的格式）
        if (true) {  // 始终使用这种处理方式
          // 这是一个特殊格式的公式，需要特殊处理
          console.log('处理公式:', processedFormula);

          // 移除可能的外层 $ 或 $$ 符号
          processedFormula = processedFormula.replace(/^\$\$|\$\$$/g, '');
          processedFormula = processedFormula.replace(/^\$|\$$/g, '');

          // 处理特殊的 \$ 格式
          processedFormula = processedFormula.replace(/\\\$/g, '');

          // 处理特殊的 LaTeX 命令
          // 处理 \frac 命令
          processedFormula = processedFormula.replace(/\\frac/g, '\\frac');

          // 处理 \circ 命令
          processedFormula = processedFormula.replace(/\\circ/g, '\\circ');

          // 处理 \tan 命令
          processedFormula = processedFormula.replace(/\\tan/g, '\\tan');

          // 处理 \cos 命令
          processedFormula = processedFormula.replace(/\\cos/g, '\\cos');

          // 处理 \sin 命令
          processedFormula = processedFormula.replace(/\\sin/g, '\\sin');

          // 处理 \sqrt 命令
          processedFormula = processedFormula.replace(/\\sqrt/g, '\\sqrt');

          // 处理特殊的括号格式，如 \sqrt{2}
          processedFormula = processedFormula.replace(/\\sqrt\{([^{}]+)\}/g, '\\sqrt{$1}');

          // 处理特殊的幂格式，如 (1-\sqrt{2})^2
          processedFormula = processedFormula.replace(/\(([^()]+)\)\^(\d+)/g, '($1)^{$2}');

          // 处理特殊的格式，如 (2)
          processedFormula = processedFormula.replace(/\((\d+)\)/g, '{$1}');

          // 使用 codecogs 在线服务渲染 LaTeX 公式
          const encodedFormula = encodeURIComponent(processedFormula);
          const imageUrl = `https://latex.codecogs.com/svg.latex?${encodedFormula}`;

          this.setData({
            imageUrl: imageUrl,
            isLoading: false,
            isApplicationProblem: false,
            errorMessage: ''
          });

          return;
        }

        // 移除可能的外层 $ 或 $$ 符号
        processedFormula = processedFormula.replace(/^\$\$|\$\$$/g, '');
        processedFormula = processedFormula.replace(/^\$|\$$/g, '');

        // 处理特殊的 \$ 格式
        processedFormula = processedFormula.replace(/\\\$/g, '');

        // 处理特殊的 LaTeX 命令
        // 处理 \frac 命令
        processedFormula = processedFormula.replace(/\\frac/g, '\\frac');

        // 处理 \circ 命令
        processedFormula = processedFormula.replace(/\\circ/g, '\\circ');

        // 处理 \tan 命令
        processedFormula = processedFormula.replace(/\\tan/g, '\\tan');

        // 处理 \cos 命令
        processedFormula = processedFormula.replace(/\\cos/g, '\\cos');

        // 处理 \sin 命令
        processedFormula = processedFormula.replace(/\\sin/g, '\\sin');

        // 处理 \sqrt 命令
        processedFormula = processedFormula.replace(/\\sqrt/g, '\\sqrt');

        // 处理特殊的括号格式，如 \sqrt{2}
        processedFormula = processedFormula.replace(/\\sqrt\{([^{}]+)\}/g, '\\sqrt{$1}');

        // 处理特殊的幂格式，如 (1-\sqrt{2})^2
        processedFormula = processedFormula.replace(/\(([^()]+)\)\^(\d+)/g, '($1)^{$2}');

        // 处理特殊的格式，如 (2)
        processedFormula = processedFormula.replace(/\((\d+)\)/g, '{$1}');

        // 使用towxml解析LaTeX公式
        const markdownWithLatex = `$$${processedFormula}$$`;
        console.log('处理后的纯数学公式:', markdownWithLatex);

        const article = towxml(markdownWithLatex, 'markdown', {
          theme: 'light'
        });

        this.setData({
          article: article,
          isLoading: false,
          isApplicationProblem: false,
          errorMessage: ''
        });
      } catch (error) {
        console.error('公式渲染错误:', error);
        // 渲染失败时，显示原始文本
        this.setData({
          imageUrl: '',
          isLoading: false,
          isApplicationProblem: true,
          formattedText: formula,
          errorMessage: ''
        });
      }
    },

    // 图片加载失败处理
    onImageError: function(e) {
      console.error('图片加载失败:', e);
      this.setData({
        isLoading: false,
        errorMessage: '公式图片加载失败，请检查公式语法'
      });
    },

    // 图片加载成功处理
    onImageLoad: function() {
      this.setData({
        isLoading: false
      });
    }
  }
})
