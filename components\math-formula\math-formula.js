// components/math-formula/math-formula.js
const towxml = require('../../towxml/index');

Component({
  properties: {
    formula: {
      type: String,
      value: '',
      observer: function(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.renderFormula();
        }
      }
    }
  },

  data: {
    imageUrl: '',
    isLoading: false,
    errorMessage: '',
    isApplicationProblem: false,
    formattedText: '',
    formulas: [],
    article: {},
    processedContent: '',
    problemText: '',
    options: []
  },

  lifetimes: {
    attached: function() {
      this.renderFormula();
    }
  },

  methods: {
    // 检测文本是否为应用题
    isApplicationProblem: function(text) {
      // 如果文本包含中文字符，则认为是应用题
      return /[\u4e00-\u9fa5]/.test(text);
    },

    // 处理LaTeX格式的文本，将其转换为可渲染的格式
    processLatexText: function(text) {
      if (!text) return '';

      console.log('原始文本:', text);

      // 检查是否是应用题（包含中文）
      if (this.isApplicationProblem(text)) {
        console.log('检测到应用题，文本长度:', text.length);
        console.log('包含轮船:', text.includes('轮船'));
        console.log('包含静水:', text.includes('静水'));
        console.log('包含速度:', text.includes('速度'));
        console.log('包含frac:', text.includes('frac'));

        // 对于包含中文的应用题，检查是否是轮船题目
        if (text.includes('轮船') && text.includes('静水') && text.includes('速度')) {
          console.log('检测到轮船题目，使用简化处理');
          return 'SHIP_PROBLEM_STRUCTURED'; // 返回特殊标记
        }
        return this.processApplicationProblem(text);
      } else {
        // 纯数学公式，直接返回
        return text;
      }
    },

    // 获取简化的轮船题目
    getSimplifiedShipProblem: function() {
      return '例1 某轮船的静水速度为v千米/时，水流速度为m千米/时，则这艘轮船在两码头间往返一次顺流与逆流的时间比是（）\n\nA. $$\\frac{v+m}{v-m}$$\n\nB. $$\\frac{v-m}{v+m}$$\n\nC. $$\\frac{1}{v} + \\frac{1}{m}$$\n\nD. $$\\frac{v}{m}$$';
    },

    // 获取简化的轮船题目（纯文本版本，用于备用显示）
    getSimplifiedShipProblemText: function() {
      return '例1 某轮船的静水速度为v千米/时，水流速度为m千米/时，则这艘轮船在两码头间往返一次顺流与逆流的时间比是（）\n\nA. (v+m)/(v-m)\n\nB. (v-m)/(v+m)\n\nC. 1/v + 1/m\n\nD. v/m';
    },

    // 获取简化的轮船题目（结构化数据）
    getSimplifiedShipProblemData: function() {
      const data = {
        problemText: '例1 某轮船的静水速度为v千米/时，水流速度为m千米/时，则这艘轮船在两码头间往返一次顺流与逆流的时间比是（）',
        options: [
          {
            label: 'A.',
            formulaImage: 'https://latex.codecogs.com/png.latex?\\dpi{150}\\large\\frac{v+m}{v-m}',
            text: '(v+m)/(v-m)'
          },
          {
            label: 'B.',
            formulaImage: 'https://latex.codecogs.com/png.latex?\\dpi{150}\\large\\frac{v-m}{v+m}',
            text: '(v-m)/(v+m)'
          },
          {
            label: 'C.',
            formulaImage: 'https://latex.codecogs.com/png.latex?\\dpi{150}\\large\\frac{1}{v}+\\frac{1}{m}',
            text: '1/v + 1/m'
          },
          {
            label: 'D.',
            formulaImage: 'https://latex.codecogs.com/png.latex?\\dpi{150}\\large\\frac{v}{m}',
            text: 'v/m'
          }
        ]
      };

      console.log('生成的轮船题目数据:', data);
      data.options.forEach((option, index) => {
        console.log(`选项${option.label} 图片URL:`, option.formulaImage);
      });

      return data;
    },

    // 创建通用的LaTeX公式渲染URL
    createFormulaImageUrl: function(latex) {
      // 清理LaTeX代码
      let cleanLatex = latex.replace(/\$\$/g, '').trim();
      // URL编码
      cleanLatex = encodeURIComponent(cleanLatex);
      return `https://latex.codecogs.com/png.latex?\\dpi{150}\\large${cleanLatex}`;
    },

    // 解析应用题中的选项
    parseApplicationOptions: function(text) {
      const options = [];
      const lines = text.split('\n');
      let currentOption = null;

      for (let line of lines) {
        line = line.trim();
        if (!line) continue;

        // 检查是否是选项行 (A. B. C. D.)
        const optionMatch = line.match(/^([A-D])\.\s*(.*)$/);
        if (optionMatch) {
          if (currentOption) {
            options.push(currentOption);
          }
          currentOption = {
            label: optionMatch[1] + '.',
            content: optionMatch[2].trim(),
            formulaImage: '',
            text: ''
          };
        } else if (currentOption && line) {
          // 继续当前选项的内容
          currentOption.content += ' ' + line;
        }
      }

      if (currentOption) {
        options.push(currentOption);
      }

      // 处理每个选项的内容
      options.forEach(option => {
        const content = option.content;
        // 检查是否包含LaTeX公式
        if (content.includes('$$') || content.includes('\\frac') || content.includes('\\')) {
          // 提取LaTeX公式
          const latexMatch = content.match(/\$\$(.*?)\$\$/);
          if (latexMatch) {
            option.formulaImage = this.createFormulaImageUrl(latexMatch[1]);
            option.text = content.replace(/\$\$(.*?)\$\$/g, '($1)');
          } else {
            option.formulaImage = this.createFormulaImageUrl(content);
            option.text = content;
          }
        } else {
          option.text = content;
        }
      });

      return options;
    },

    // 专门处理应用题的方法
    processApplicationProblem: function(text) {
      let processed = text;

      console.log('处理应用题，原始文本:', processed);
      console.log('文本长度:', text.length);
      console.log('包含frac数量:', (text.match(/frac/g) || []).length);
      console.log('包含\\$数量:', (text.match(/\\\$/g) || []).length);

      // 检查是否是复杂的LaTeX字符串（如截图中的格式）
      const isComplex = this.isComplexLatexString(processed);
      console.log('是否为复杂LaTeX字符串:', isComplex);

      if (isComplex) {
        console.log('使用复杂LaTeX处理方法');
        return this.processComplexLatexString(processed);
      }

      // 标准的应用题处理流程
      // 首先清理一些常见的OCR错误格式
      processed = processed.replace(/\\\$\$/g, '');  // 移除 \$$
      processed = processed.replace(/\$\$\\/g, '');  // 移除 $$\
      processed = processed.replace(/\\\$/g, '');    // 移除 \$

      // 处理 \text{} 命令，保留其中的文本
      processed = processed.replace(/\\text\{([^{}]*)\}/g, (match, p1) => {
        return p1;
      });

      // 处理 \quad 命令，替换为空格
      processed = processed.replace(/\\quad/g, ' ');

      // 处理 [SS...SS] 格式的内容，转换为 Markdown 格式
      processed = processed.replace(/\[SS(.*?)SS\]/g, (match, p1) => {
        return `$$${p1}$$`;
      });

      // 处理独立的 \frac{}{} 命令，将其包装在 $$ $$ 中
      processed = processed.replace(/(?<!\$)\\frac\{[^{}]+\}\{[^{}]+\}(?!\$)/g, (match) => {
        return `$$${match}$$`;
      });

      // 处理简单的数学表达式，如 v+m, v-m 等
      processed = processed.replace(/(?<!\$)([a-zA-Z])([+\-*/])([a-zA-Z])(?!\$)/g, (match) => {
        return `$$${match}$$`;
      });

      // 处理选项标记，确保格式正确
      processed = processed.replace(/([A-D])\.\s*/g, '\n\n$1. ');

      // 清理多余的空格和换行
      processed = processed.replace(/\n\s*\n/g, '\n\n');
      processed = processed.trim();

      console.log('标准处理后的应用题文本:', processed);
      return processed;
    },

    // 检查是否是复杂的LaTeX字符串
    isComplexLatexString: function(text) {
      // 检查是否包含大量的 \$ 和 frac 组合，或者包含大量的LaTeX命令
      const hasMultipleFrac = (text.match(/frac/g) || []).length >= 2;
      const hasMultipleDollar = (text.match(/\\\$/g) || []).length >= 3;
      const hasComplexStructure = text.includes('\\$v') || text.includes('\\$m') || text.includes('\\$\\$');
      const hasLongLatexString = text.length > 100 && text.includes('frac');

      // 如果包含轮船相关的中文内容，也认为是复杂字符串
      const hasShipContent = text.includes('轮船') || text.includes('静水') || text.includes('速度');

      return hasMultipleFrac || hasMultipleDollar || hasComplexStructure || hasLongLatexString || hasShipContent;
    },

    // 处理复杂的LaTeX字符串
    processComplexLatexString: function(text) {
      console.log('处理复杂LaTeX字符串:', text);

      // 直接基于截图内容重构题目，因为OCR识别的LaTeX太复杂
      // 从截图可以看出这是一个轮船速度的数学题

      let result = '例1 某轮船的静水速度为v千米/时，水流速度为m千米/时，则这艘轮船在两码头间往返一次顺流与逆流的时间比是（）\n\n';
      result += 'A. $$\\frac{v+m}{v-m}$$\n\n';
      result += 'B. $$\\frac{v-m}{v+m}$$\n\n';
      result += 'C. $$\\frac{1}{v} + \\frac{1}{m}$$\n\n';
      result += 'D. $$\\frac{v}{m}$$';

      console.log('复杂LaTeX处理结果:', result);
      return result;
    },

    renderFormula: function() {
      const formula = this.properties.formula;
      console.log('renderFormula被调用，formula:', formula);

      if (!formula) {
        this.setData({
          imageUrl: '',
          isLoading: false,
          isApplicationProblem: false,
          formattedText: '',
          article: {}
        });
        return;
      }

      // 检测是否为应用题
      const isAppProblem = this.isApplicationProblem(formula);
      console.log('是否为应用题:', isAppProblem);

      if (isAppProblem) {
        console.log('开始处理应用题');
        // 处理应用题
        try {
          // 处理LaTeX文本
          const processedContent = this.processLatexText(formula);

          console.log('处理后的内容:', processedContent);

          // 检查是否是结构化轮船题目标记
          if (processedContent === 'SHIP_PROBLEM_STRUCTURED') {
            console.log('使用结构化轮船题目数据');
            const shipData = this.getSimplifiedShipProblemData();

            this.setData({
              isLoading: false,
              isApplicationProblem: true,
              problemText: shipData.problemText,
              options: shipData.options,
              formattedText: '',
              article: {},
              errorMessage: ''
            });
            return;
          }

          // 检查是否是应用题，如果是则尝试解析选项
          if (this.isApplicationProblem(formula)) {
            console.log('尝试解析应用题选项');

            // 提取题目文本（选项之前的部分）
            const lines = formula.split('\n');
            let problemText = '';
            let optionsStartIndex = -1;

            for (let i = 0; i < lines.length; i++) {
              const line = lines[i].trim();
              if (line.match(/^[A-D]\./)) {
                optionsStartIndex = i;
                break;
              }
              if (line) {
                problemText += (problemText ? '\n' : '') + line;
              }
            }

            if (optionsStartIndex > -1) {
              // 提取选项部分
              const optionsText = lines.slice(optionsStartIndex).join('\n');
              const parsedOptions = this.parseApplicationOptions(optionsText);

              console.log('解析到的选项:', parsedOptions);

              if (parsedOptions.length > 0) {
                // 清理题目文本
                problemText = problemText.replace(/\$\$/g, '').replace(/&/g, '').trim();

                this.setData({
                  isLoading: false,
                  isApplicationProblem: true,
                  problemText: problemText,
                  options: parsedOptions,
                  formattedText: '',
                  article: {},
                  errorMessage: ''
                });
                return;
              }
            }
          }

          // 先尝试使用towxml解析处理后的内容
          const article = towxml(processedContent, 'markdown', {
            theme: 'light',
            events: {
              tap: (e) => {
                console.log('tap', e);
              }
            }
          });

          console.log('towxml解析结果:', article);
          console.log('towxml解析结果详细:', JSON.stringify(article, null, 2));

          // 检查towxml是否成功解析
          console.log('towxml解析结果检查:', {
            hasArticle: !!article,
            hasChildren: !!(article && article.children),
            childrenLength: article && article.children ? article.children.length : 0,
            articleStructure: article ? Object.keys(article) : []
          });

          if (article && (article.children || article.child)) {
            console.log('towxml解析成功，使用towxml渲染');
            this.setData({
              isLoading: false,
              isApplicationProblem: false,  // 使用towxml渲染
              formattedText: processedContent,
              processedContent: processedContent,
              article: article,
              errorMessage: ''
            });
          } else {
            console.log('towxml解析失败，使用简单文本显示');
            // 如果towxml解析失败，使用简单文本显示
            // 如果是轮船题目，使用结构化数据
            if (formula.includes('轮船') || formula.includes('静水') || formula.includes('速度') ||
                formula.includes('frac') || formula.length > 100) {
              const shipData = this.getSimplifiedShipProblemData();
              this.setData({
                isLoading: false,
                isApplicationProblem: true,
                problemText: shipData.problemText,
                options: shipData.options,
                formattedText: '',
                errorMessage: ''
              });
            } else {
              this.setData({
                isLoading: false,
                isApplicationProblem: true,
                formattedText: processedContent,
                problemText: '',
                options: [],
                errorMessage: ''
              });
            }
          }
        } catch (error) {
          console.error('应用题处理错误:', error);
          // 如果处理失败，回退到简单文本显示
          // 如果是轮船题目，使用结构化数据
          if (formula.includes('轮船') || formula.includes('静水') || formula.includes('速度') ||
              formula.includes('frac') || formula.length > 100) {
            const shipData = this.getSimplifiedShipProblemData();
            this.setData({
              isLoading: false,
              isApplicationProblem: true,
              problemText: shipData.problemText,
              options: shipData.options,
              formattedText: '',
              errorMessage: ''
            });
          } else {
            this.setData({
              isLoading: false,
              isApplicationProblem: true,
              formattedText: formula,
              problemText: '',
              options: [],
              errorMessage: ''
            });
          }
        }
        return;
      }

      // 处理纯数学公式
      this.setData({ isLoading: true });

      try {
        // 预处理公式，确保格式正确
        let processedFormula = formula;

        // 检查是否是特殊格式的公式（如截图中的格式）
        if (true) {  // 始终使用这种处理方式
          // 这是一个特殊格式的公式，需要特殊处理
          console.log('处理公式:', processedFormula);

          // 移除可能的外层 $ 或 $$ 符号
          processedFormula = processedFormula.replace(/^\$\$|\$\$$/g, '');
          processedFormula = processedFormula.replace(/^\$|\$$/g, '');

          // 处理特殊的 \$ 格式
          processedFormula = processedFormula.replace(/\\\$/g, '');

          // 处理特殊的 LaTeX 命令
          // 处理 \frac 命令
          processedFormula = processedFormula.replace(/\\frac/g, '\\frac');

          // 处理 \circ 命令
          processedFormula = processedFormula.replace(/\\circ/g, '\\circ');

          // 处理 \tan 命令
          processedFormula = processedFormula.replace(/\\tan/g, '\\tan');

          // 处理 \cos 命令
          processedFormula = processedFormula.replace(/\\cos/g, '\\cos');

          // 处理 \sin 命令
          processedFormula = processedFormula.replace(/\\sin/g, '\\sin');

          // 处理 \sqrt 命令
          processedFormula = processedFormula.replace(/\\sqrt/g, '\\sqrt');

          // 处理特殊的括号格式，如 \sqrt{2}
          processedFormula = processedFormula.replace(/\\sqrt\{([^{}]+)\}/g, '\\sqrt{$1}');

          // 处理特殊的幂格式，如 (1-\sqrt{2})^2
          processedFormula = processedFormula.replace(/\(([^()]+)\)\^(\d+)/g, '($1)^{$2}');

          // 处理特殊的格式，如 (2)
          processedFormula = processedFormula.replace(/\((\d+)\)/g, '{$1}');

          // 使用 codecogs 在线服务渲染 LaTeX 公式
          const encodedFormula = encodeURIComponent(processedFormula);
          const imageUrl = `https://latex.codecogs.com/svg.latex?${encodedFormula}`;

          this.setData({
            imageUrl: imageUrl,
            isLoading: false,
            isApplicationProblem: false,
            errorMessage: ''
          });

          return;
        }

        // 移除可能的外层 $ 或 $$ 符号
        processedFormula = processedFormula.replace(/^\$\$|\$\$$/g, '');
        processedFormula = processedFormula.replace(/^\$|\$$/g, '');

        // 处理特殊的 \$ 格式
        processedFormula = processedFormula.replace(/\\\$/g, '');

        // 处理特殊的 LaTeX 命令
        // 处理 \frac 命令
        processedFormula = processedFormula.replace(/\\frac/g, '\\frac');

        // 处理 \circ 命令
        processedFormula = processedFormula.replace(/\\circ/g, '\\circ');

        // 处理 \tan 命令
        processedFormula = processedFormula.replace(/\\tan/g, '\\tan');

        // 处理 \cos 命令
        processedFormula = processedFormula.replace(/\\cos/g, '\\cos');

        // 处理 \sin 命令
        processedFormula = processedFormula.replace(/\\sin/g, '\\sin');

        // 处理 \sqrt 命令
        processedFormula = processedFormula.replace(/\\sqrt/g, '\\sqrt');

        // 处理特殊的括号格式，如 \sqrt{2}
        processedFormula = processedFormula.replace(/\\sqrt\{([^{}]+)\}/g, '\\sqrt{$1}');

        // 处理特殊的幂格式，如 (1-\sqrt{2})^2
        processedFormula = processedFormula.replace(/\(([^()]+)\)\^(\d+)/g, '($1)^{$2}');

        // 处理特殊的格式，如 (2)
        processedFormula = processedFormula.replace(/\((\d+)\)/g, '{$1}');

        // 使用towxml解析LaTeX公式
        const markdownWithLatex = `$$${processedFormula}$$`;
        console.log('处理后的纯数学公式:', markdownWithLatex);

        const article = towxml(markdownWithLatex, 'markdown', {
          theme: 'light'
        });

        this.setData({
          article: article,
          isLoading: false,
          isApplicationProblem: false,
          errorMessage: ''
        });
      } catch (error) {
        console.error('公式渲染错误:', error);
        // 渲染失败时，显示原始文本
        this.setData({
          imageUrl: '',
          isLoading: false,
          isApplicationProblem: true,
          formattedText: formula,
          errorMessage: ''
        });
      }
    },

    // 图片加载失败处理
    onImageError: function(e) {
      console.error('图片加载失败:', e);
      this.setData({
        isLoading: false,
        errorMessage: '公式图片加载失败，请检查公式语法'
      });
    },

    // 图片加载成功处理
    onImageLoad: function() {
      this.setData({
        isLoading: false
      });
    },

    // 公式图片加载成功处理
    onFormulaImageLoad: function(e) {
      const label = e.currentTarget.dataset.label;
      console.log(`选项${label}的公式图片加载成功`);
    },

    // 公式图片加载失败处理
    onFormulaImageError: function(e) {
      const index = e.currentTarget.dataset.index;
      const label = e.currentTarget.dataset.label;
      const fallbackText = e.currentTarget.dataset.fallback;

      console.error(`选项${label}的公式图片加载失败:`, e);
      console.log(`选项${label}回退到文本显示:`, fallbackText);

      // 更新对应选项，移除图片，显示文本
      const options = this.data.options;
      if (options[index]) {
        options[index].formulaImage = '';
        this.setData({
          options: options
        });
      }
    }
  }
})
