// components/math-formula/math-formula.js
const towxml = require('../../towxml/index');

Component({
  properties: {
    formula: {
      type: String,
      value: '',
      observer: function(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.renderFormula();
        }
      }
    }
  },

  data: {
    imageUrl: '',
    isLoading: false,
    errorMessage: '',
    isApplicationProblem: false,
    formattedText: '',
    formulas: [],
    article: {},
    processedContent: ''
  },

  lifetimes: {
    attached: function() {
      this.renderFormula();
    }
  },

  methods: {
    // 检测文本是否为应用题
    isApplicationProblem: function(text) {
      // 如果文本包含中文字符，则认为是应用题
      return /[\u4e00-\u9fa5]/.test(text);
    },

    // 处理LaTeX格式的文本，将其转换为可渲染的格式
    processLatexText: function(text) {
      if (!text) return '';

      // 检查是否是应用题（包含中文）
      if (this.isApplicationProblem(text)) {
        // 处理应用题中的LaTeX公式
        let processed = text;

        console.log('原始应用题文本:', processed);

        // 首先处理可能的错误格式，如 \$$ 和 $$\
        processed = processed.replace(/\\\$\$/g, '');  // 移除 \$$
        processed = processed.replace(/\$\$\\/g, '');  // 移除 $$\

        // 处理 \begin{align*} 和 \end{align*} 标记
        processed = processed.replace(/\\begin\{align\*\}([\s\S]*?)\\end\{align\*\}/g, (match, p1) => {
          return `$$${p1}$$`;
        });

        // 处理 \text{} 命令，保留其中的文本
        processed = processed.replace(/\\text\{([^{}]*)\}/g, (match, p1) => {
          return p1;
        });

        // 处理 \quad 命令，替换为空格
        processed = processed.replace(/\\quad/g, ' ');

        // 处理 \end{align} 命令
        processed = processed.replace(/\\end\{align\}/g, '');

        // 处理 [SS...SS] 格式的内容，转换为 Markdown 格式
        processed = processed.replace(/\[SS(.*?)SS\]/g, (match, p1) => {
          return `$$${p1}$$`;
        });

        // 处理 \frac{}{} 命令，将其包装在 $$ $$ 中
        processed = processed.replace(/(\\frac\{[^{}]+\}\{[^{}]+\})/g, (match) => {
          return `$$${match}$$`;
        });

        // 处理 \$frac 格式的公式
        processed = processed.replace(/\\\$frac/g, '$$\\frac');
        processed = processed.replace(/\$\$\$/g, '$$');

        // 处理 OCR 识别出的特殊格式
        processed = processed.replace(/\$frac/g, '$$\\frac');

        // 处理简单的数学表达式，如 v+m, v-m 等（但要避免处理选项标记）
        processed = processed.replace(/([a-zA-Z])([+\-*/])([a-zA-Z])/g, (match, p1, p2, p3) => {
          // 检查前后是否有中文字符，如果有，则可能是在文本中，不需要处理
          return `$$${match}$$`;
        });

        // 处理选项标记，确保格式正确
        processed = processed.replace(/([A-D])\.\s*/g, '\n\n$1. ');

        // 处理特殊的公式格式，如 \frac{v+m}{v-m}
        processed = processed.replace(/\$\$\\frac\{([^{}]+)\}\{([^{}]+)\}\$\$/g, (match, p1, p2) => {
          return `$$\\frac{${p1}}{${p2}}$$`;
        });

        // 移除多余的反斜杠
        processed = processed.replace(/\\([^a-zA-Z{}])/g, '$1');

        // 处理换行，确保选项之间有适当的间距
        processed = processed.replace(/\n\s*\n/g, '\n\n');

        console.log('处理后的应用题文本:', processed);

        return processed;
      } else {
        // 纯数学公式，直接返回
        return text;
      }
    },

    renderFormula: function() {
      const formula = this.properties.formula;
      if (!formula) {
        this.setData({
          imageUrl: '',
          isLoading: false,
          isApplicationProblem: false,
          formattedText: '',
          article: {}
        });
        return;
      }

      // 检测是否为应用题
      const isAppProblem = this.isApplicationProblem(formula);

      if (isAppProblem) {
        // 处理应用题
        try {
          // 处理LaTeX文本
          const processedContent = this.processLatexText(formula);

          console.log('处理后的内容:', processedContent);

          // 使用towxml解析处理后的内容
          const article = towxml(processedContent, 'markdown', {
            theme: 'light',
            events: {
              tap: (e) => {
                console.log('tap', e);
              }
            }
          });

          console.log('towxml解析结果:', article);

          this.setData({
            isLoading: false,
            isApplicationProblem: true,
            formattedText: formula,
            processedContent: processedContent,
            article: article,
            errorMessage: ''
          });
        } catch (error) {
          console.error('应用题处理错误:', error);
          this.setData({
            isLoading: false,
            isApplicationProblem: true,
            formattedText: formula,
            errorMessage: '应用题处理失败: ' + error.message
          });
        }
        return;
      }

      // 处理纯数学公式
      this.setData({ isLoading: true });

      try {
        // 预处理公式，确保格式正确
        let processedFormula = formula;

        // 检查是否是特殊格式的公式（如截图中的格式）
        if (true) {  // 始终使用这种处理方式
          // 这是一个特殊格式的公式，需要特殊处理
          console.log('处理公式:', processedFormula);

          // 移除可能的外层 $ 或 $$ 符号
          processedFormula = processedFormula.replace(/^\$\$|\$\$$/g, '');
          processedFormula = processedFormula.replace(/^\$|\$$/g, '');

          // 处理特殊的 \$ 格式
          processedFormula = processedFormula.replace(/\\\$/g, '');

          // 处理特殊的 LaTeX 命令
          // 处理 \frac 命令
          processedFormula = processedFormula.replace(/\\frac/g, '\\frac');

          // 处理 \circ 命令
          processedFormula = processedFormula.replace(/\\circ/g, '\\circ');

          // 处理 \tan 命令
          processedFormula = processedFormula.replace(/\\tan/g, '\\tan');

          // 处理 \cos 命令
          processedFormula = processedFormula.replace(/\\cos/g, '\\cos');

          // 处理 \sin 命令
          processedFormula = processedFormula.replace(/\\sin/g, '\\sin');

          // 处理 \sqrt 命令
          processedFormula = processedFormula.replace(/\\sqrt/g, '\\sqrt');

          // 处理特殊的括号格式，如 \sqrt{2}
          processedFormula = processedFormula.replace(/\\sqrt\{([^{}]+)\}/g, '\\sqrt{$1}');

          // 处理特殊的幂格式，如 (1-\sqrt{2})^2
          processedFormula = processedFormula.replace(/\(([^()]+)\)\^(\d+)/g, '($1)^{$2}');

          // 处理特殊的格式，如 (2)
          processedFormula = processedFormula.replace(/\((\d+)\)/g, '{$1}');

          // 使用 codecogs 在线服务渲染 LaTeX 公式
          const encodedFormula = encodeURIComponent(processedFormula);
          const imageUrl = `https://latex.codecogs.com/svg.latex?${encodedFormula}`;

          this.setData({
            imageUrl: imageUrl,
            isLoading: false,
            isApplicationProblem: false,
            errorMessage: ''
          });

          return;
        }

        // 移除可能的外层 $ 或 $$ 符号
        processedFormula = processedFormula.replace(/^\$\$|\$\$$/g, '');
        processedFormula = processedFormula.replace(/^\$|\$$/g, '');

        // 处理特殊的 \$ 格式
        processedFormula = processedFormula.replace(/\\\$/g, '');

        // 处理特殊的 LaTeX 命令
        // 处理 \frac 命令
        processedFormula = processedFormula.replace(/\\frac/g, '\\frac');

        // 处理 \circ 命令
        processedFormula = processedFormula.replace(/\\circ/g, '\\circ');

        // 处理 \tan 命令
        processedFormula = processedFormula.replace(/\\tan/g, '\\tan');

        // 处理 \cos 命令
        processedFormula = processedFormula.replace(/\\cos/g, '\\cos');

        // 处理 \sin 命令
        processedFormula = processedFormula.replace(/\\sin/g, '\\sin');

        // 处理 \sqrt 命令
        processedFormula = processedFormula.replace(/\\sqrt/g, '\\sqrt');

        // 处理特殊的括号格式，如 \sqrt{2}
        processedFormula = processedFormula.replace(/\\sqrt\{([^{}]+)\}/g, '\\sqrt{$1}');

        // 处理特殊的幂格式，如 (1-\sqrt{2})^2
        processedFormula = processedFormula.replace(/\(([^()]+)\)\^(\d+)/g, '($1)^{$2}');

        // 处理特殊的格式，如 (2)
        processedFormula = processedFormula.replace(/\((\d+)\)/g, '{$1}');

        // 使用towxml解析LaTeX公式
        const markdownWithLatex = `$$${processedFormula}$$`;
        console.log('处理后的纯数学公式:', markdownWithLatex);

        const article = towxml(markdownWithLatex, 'markdown', {
          theme: 'light'
        });

        this.setData({
          article: article,
          isLoading: false,
          isApplicationProblem: false,
          errorMessage: ''
        });
      } catch (error) {
        console.error('公式渲染错误:', error);
        // 渲染失败时，显示原始文本
        this.setData({
          imageUrl: '',
          isLoading: false,
          isApplicationProblem: true,
          formattedText: formula,
          errorMessage: ''
        });
      }
    },

    // 图片加载失败处理
    onImageError: function(e) {
      console.error('图片加载失败:', e);
      this.setData({
        isLoading: false,
        errorMessage: '公式图片加载失败，请检查公式语法'
      });
    },

    // 图片加载成功处理
    onImageLoad: function() {
      this.setData({
        isLoading: false
      });
    }
  }
})
