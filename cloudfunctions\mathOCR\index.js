// 云函数入口文件
const cloud = require('wx-server-sdk')
const tencentcloud = require('tencentcloud-sdk-nodejs')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })

// 导入对应产品模块的client models
const ocr = tencentcloud.ocr.v20181119
const OcrClient = ocr.Client

// 创建OCR客户端
const client = new OcrClient({
  credential: {
    secretId: process.env.SecretId,
    secretKey: process.env.SecretKey,
  },
  region: 'ap-guangzhou',
  profile: {
    httpProfile: {
      endpoint: 'ocr.tencentcloudapi.com',
    },
  },
})

// 云函数入口函数
exports.main = async (event, context) => {
  const { fileID } = event

  console.log('接收到数学公式OCR请求，fileID:', fileID);
  console.log('环境变量检查 - SecretId存在:', !!process.env.SecretId);
  console.log('环境变量检查 - SecretKey存在:', !!process.env.SecretKey);

  try {
    // 下载云存储中的图片
    console.log('开始从云存储下载图片');
    const result = await cloud.downloadFile({
      fileID: fileID,
    })

    console.log('图片下载成功，大小:', result.fileContent.length, '字节');

    const buffer = result.fileContent
    const base64Image = buffer.toString('base64')

    // 调用数学公式识别接口
    const params = {
      ImageBase64: base64Image,
    }

    // 打印请求参数（不包含图片内容）
    console.log('请求参数:', {
      ...params,
      ImageBase64: '图片内容已省略...'
    });

    try {
      // 调用OCR API
      console.log('开始调用腾讯数学公式识别API');
      const ocrResult = await client.RecognizeFormulaOCR(params)

      console.log('数学公式识别API调用成功');

      // 详细记录返回结果的结构
      console.log('返回结果类型:', typeof ocrResult);
      console.log('返回结果结构:', Object.keys(ocrResult));
      console.log('完整返回结果:', JSON.stringify(ocrResult));

      // 检查是否有FormulaInfoList（新版API返回格式）
      if (ocrResult.FormulaInfoList && ocrResult.FormulaInfoList.length > 0) {
        console.log('FormulaInfoList长度:', ocrResult.FormulaInfoList.length);
        console.log('第一个公式:', ocrResult.FormulaInfoList[0].DetectedText);

        // 直接返回原始结果，让前端处理
        return ocrResult;
      }
      // 检查是否有FormulaInfos（旧版API返回格式）
      else if (ocrResult.FormulaInfos && ocrResult.FormulaInfos.length > 0) {
        console.log('FormulaInfos长度:', ocrResult.FormulaInfos.length);
        console.log('第一个公式:', ocrResult.FormulaInfos[0].DetectedText);

        return {
          ocrText: ocrResult.FormulaInfos[0].DetectedText,
          errCode: 0,
          errMsg: "数学公式识别成功",
          version: "v3_formulaOCR"
        };
      } else if (ocrResult.TextDetections && ocrResult.TextDetections.length > 0) {
        // 如果返回的是通用文字识别结果
        // 检查是否包含中文字符，如果包含，则可能是应用题
        const containsChinese = ocrResult.TextDetections.some(item =>
          /[\u4e00-\u9fa5]/.test(item.DetectedText)
        );

        if (containsChinese) {
          console.log('检测到中文字符，可能是应用题');

          // 按照 Y 坐标排序，确保文本按照正确的顺序组合
          const sortedDetections = [...ocrResult.TextDetections].sort((a, b) => {
            // 如果 Y 坐标相差不大，则按照 X 坐标排序
            if (Math.abs(a.ItemPolygon.Y - b.ItemPolygon.Y) < 10) {
              return a.ItemPolygon.X - b.ItemPolygon.X;
            }
            return a.ItemPolygon.Y - b.ItemPolygon.Y;
          });

          let fullText = '';
          let currentY = -1;
          let currentLine = '';

          // 按行组织文本
          sortedDetections.forEach(item => {
            if (currentY === -1 || Math.abs(item.ItemPolygon.Y - currentY) > 10) {
              // 新的一行
              if (currentLine) {
                fullText += currentLine + '\n';
              }
              currentLine = item.DetectedText;
              currentY = item.ItemPolygon.Y;
            } else {
              // 同一行
              currentLine += ' ' + item.DetectedText;
            }
          });

          // 添加最后一行
          if (currentLine) {
            fullText += currentLine;
          }

          // 处理应用题中的LaTeX公式
          // 检查是否包含数学公式特征
          const hasMathFeatures = /[a-zA-Z][+\-*/][a-zA-Z]|\\frac|\\sqrt|\\text/.test(fullText);

          if (hasMathFeatures) {
            console.log('检测到可能包含数学公式的应用题');

            // 首先处理可能的错误格式，如 \$$ 和 $$\
            fullText = fullText.replace(/\\\$\$/g, '');  // 移除 \$$
            fullText = fullText.replace(/\$\$\\/g, '');  // 移除 $$\

            // 处理可能的LaTeX命令
            // 1. 将 \text{内容} 转换为普通文本
            fullText = fullText.replace(/\\text\{([^{}]*)\}/g, '$1');

            // 2. 处理 \quad 命令
            fullText = fullText.replace(/\\quad/g, ' ');

            // 3. 处理 \begin{align} 和 \end{align} 标记
            fullText = fullText.replace(/\\begin\{align\*?\}|\\end\{align\*?\}/g, '');

            // 4. 处理选项标记，如 A. B. C. D.，确保格式正确
            fullText = fullText.replace(/([A-D])\.\s*/g, '\n\n$1. ');

            // 5. 处理 \frac{}{} 命令，将其包装在 $$ $$ 中
            fullText = fullText.replace(/(\\frac\{[^{}]+\}\{[^{}]+\})/g, (match) => {
              return `$$${match}$$`;
            });

            // 6. 处理简单的数学表达式，如 v+m, v-m 等
            fullText = fullText.replace(/([a-zA-Z])([+\-*/])([a-zA-Z])/g, (match) => {
              return `$$${match}$$`;
            });

            // 7. 处理特殊的公式格式，如 \frac{v+m}{v-m}
            fullText = fullText.replace(/\$\$\\frac\{([^{}]+)\}\{([^{}]+)\}\$\$/g, (match, p1, p2) => {
              return `$$\\frac{${p1}}{${p2}}$$`;
            });

            // 8. 移除多余的反斜杠
            fullText = fullText.replace(/\\([^a-zA-Z{}])/g, '$1');

            // 9. 处理 \$frac 格式的公式
            fullText = fullText.replace(/\\\$frac/g, '$$\\frac');
            fullText = fullText.replace(/\$\$\$/g, '$$');

            // 10. 处理 OCR 识别出的特殊格式
            fullText = fullText.replace(/\$frac/g, '$$\\frac');

            // 11. 将数学公式部分用 [SS...SS] 包裹，方便前端处理
            const mathParts = fullText.match(/\$\$.*?\$\$/g);
            if (mathParts && mathParts.length > 0) {
              mathParts.forEach(part => {
                const content = part.replace(/\$\$/g, '');
                fullText = fullText.replace(part, `[SS${content}SS]`);
              });
            }
          }

          return {
            ocrText: fullText.trim(),
            errCode: 0,
            errMsg: "应用题识别成功",
            version: "v3_applicationProblem",
            isApplicationProblem: true
          };
        } else {
          // 普通文本，可能是简单的公式
          let fullText = '';
          ocrResult.TextDetections.forEach(item => {
            fullText += item.DetectedText + ' ';
          });

          return {
            ocrText: fullText.trim(),
            errCode: 0,
            errMsg: "文字识别成功（非公式）",
            version: "v3_generalOCR"
          };
        }
      } else if (ocrResult.ocrText) {
        // 如果已经是我们需要的格式，直接返回
        return ocrResult;
      } else {
        // 如果没有识别到公式，返回空结果
        return {
          ocrText: "",
          errCode: 0,
          errMsg: "未识别到数学公式",
          version: "v3_formulaOCR",
          rawResult: JSON.stringify(ocrResult)
        };
      }
    } catch (apiError) {
      console.error('API调用出错:', apiError);

      // 尝试使用通用文字识别作为备选
      try {
        console.log('尝试使用通用文字识别作为备选');
        const generalResult = await client.GeneralBasicOCR(params);

        if (generalResult.TextDetections && generalResult.TextDetections.length > 0) {
          // 检查是否包含中文字符，如果包含，则可能是应用题
          const containsChinese = generalResult.TextDetections.some(item =>
            /[\u4e00-\u9fa5]/.test(item.DetectedText)
          );

          if (containsChinese) {
            console.log('备选识别检测到中文字符，可能是应用题');

            // 按照 Y 坐标排序，确保文本按照正确的顺序组合
            const sortedDetections = [...generalResult.TextDetections].sort((a, b) => {
              // 如果 Y 坐标相差不大，则按照 X 坐标排序
              if (Math.abs(a.ItemPolygon.Y - b.ItemPolygon.Y) < 10) {
                return a.ItemPolygon.X - b.ItemPolygon.X;
              }
              return a.ItemPolygon.Y - b.ItemPolygon.Y;
            });

            let fullText = '';
            let currentY = -1;
            let currentLine = '';

            // 按行组织文本
            sortedDetections.forEach(item => {
              if (currentY === -1 || Math.abs(item.ItemPolygon.Y - currentY) > 10) {
                // 新的一行
                if (currentLine) {
                  fullText += currentLine + '\n';
                }
                currentLine = item.DetectedText;
                currentY = item.ItemPolygon.Y;
              } else {
                // 同一行
                currentLine += ' ' + item.DetectedText;
              }
            });

            // 添加最后一行
            if (currentLine) {
              fullText += currentLine;
            }

            // 处理应用题中的LaTeX公式
            // 检查是否包含数学公式特征
            const hasMathFeatures = /[a-zA-Z][+\-*/][a-zA-Z]|\\frac|\\sqrt|\\text/.test(fullText);

            if (hasMathFeatures) {
              console.log('备选识别检测到可能包含数学公式的应用题');

              // 首先处理可能的错误格式，如 \$$ 和 $$\
              fullText = fullText.replace(/\\\$\$/g, '');  // 移除 \$$
              fullText = fullText.replace(/\$\$\\/g, '');  // 移除 $$\

              // 处理可能的LaTeX命令
              // 1. 将 \text{内容} 转换为普通文本
              fullText = fullText.replace(/\\text\{([^{}]*)\}/g, '$1');

              // 2. 处理 \quad 命令
              fullText = fullText.replace(/\\quad/g, ' ');

              // 3. 处理 \begin{align} 和 \end{align} 标记
              fullText = fullText.replace(/\\begin\{align\*?\}|\\end\{align\*?\}/g, '');

              // 4. 处理选项标记，如 A. B. C. D.
              fullText = fullText.replace(/([A-D])\.\s*/g, '$1. ');

              // 5. 处理 \frac{}{} 命令，将其包装在 $$ $$ 中
              fullText = fullText.replace(/(\\frac\{[^{}]+\}\{[^{}]+\})/g, (match) => {
                return `$$${match}$$`;
              });

              // 6. 处理简单的数学表达式，如 v+m, v-m 等
              fullText = fullText.replace(/([a-zA-Z])([+\-*/])([a-zA-Z])/g, (match) => {
                return `$$${match}$$`;
              });

              // 7. 处理特殊的公式格式，如 \frac{v+m}{v-m}
              fullText = fullText.replace(/\$\$\\frac\{([^{}]+)\}\{([^{}]+)\}\$\$/g, (match, p1, p2) => {
                return `$$\\frac{${p1}}{${p2}}$$`;
              });

              // 8. 移除多余的反斜杠
              fullText = fullText.replace(/\\([^a-zA-Z{}])/g, '$1');

              // 9. 处理 \$frac 格式的公式
              fullText = fullText.replace(/\\\$frac/g, '$$\\frac');
              fullText = fullText.replace(/\$\$\$/g, '$$');

              // 10. 处理 OCR 识别出的特殊格式
              fullText = fullText.replace(/\$frac/g, '$$\\frac');

              // 11. 将数学公式部分用 [SS...SS] 包裹，方便前端处理
              const mathParts = fullText.match(/\$\$.*?\$\$/g);
              if (mathParts && mathParts.length > 0) {
                mathParts.forEach(part => {
                  const content = part.replace(/\$\$/g, '');
                  fullText = fullText.replace(part, `[SS${content}SS]`);
                });
              }
            }

            return {
              ocrText: fullText.trim(),
              errCode: 0,
              errMsg: "应用题识别成功（备选）",
              version: "v3_applicationProblem_fallback",
              isApplicationProblem: true
            };
          } else {
            // 普通文本，可能是简单的公式
            let fullText = '';
            generalResult.TextDetections.forEach(item => {
              fullText += item.DetectedText + ' ';
            });

            return {
              ocrText: fullText.trim(),
              errCode: 0,
              errMsg: "通用文字识别成功（公式识别失败）",
              version: "v3_generalOCR_fallback"
            };
          }
        }
      } catch (fallbackError) {
        console.error('备选识别也失败:', fallbackError);
      }

      throw apiError; // 重新抛出原始错误
    }
  } catch (error) {
    console.error('OCR处理过程中出错:', error);
    return {
      success: false,
      error: error.message,
      stack: error.stack
    }
  }
}
