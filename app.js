// app.js
App({
  onLaunch: function () {
    // Initialize cloud environment
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        env: 'cloud1-1gtocir808c2969e', // 您的云环境ID
        traceUser: true,
      })
    }

    // Check login status
    this.checkLoginStatus();

    // Initialize usage counter
    this.initUsageCounter();
  },

  globalData: {
    userInfo: null,
    isLoggedIn: false,
    usageCount: 0,
    maxFreeUsage: 5,
    isPremiumUser: false
  },

  checkLoginStatus: function() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.globalData.userInfo = userInfo;
      this.globalData.isLoggedIn = true;
    }
  },

  initUsageCounter: function() {
    const usageCount = wx.getStorageSync('usageCount') || 0;
    const isPremiumUser = wx.getStorageSync('isPremiumUser') || false;

    this.globalData.usageCount = usageCount;
    this.globalData.isPremiumUser = isPremiumUser;
  },

  incrementUsage: function() {
    this.globalData.usageCount += 1;
    wx.setStorageSync('usageCount', this.globalData.usageCount);
    return this.globalData.usageCount;
  },

  resetUsage: function() {
    this.globalData.usageCount = 0;
    wx.setStorageSync('usageCount', 0);
  },

  setPremiumStatus: function(status) {
    this.globalData.isPremiumUser = status;
    wx.setStorageSync('isPremiumUser', status);
  },

  checkUsageLimit: function() {
    if (this.globalData.isPremiumUser) {
      return true; // Premium users have unlimited usage
    }
    return this.globalData.usageCount < this.globalData.maxFreeUsage;
  }
})
