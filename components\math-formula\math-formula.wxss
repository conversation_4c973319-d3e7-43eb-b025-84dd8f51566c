/* components/math-formula/math-formula.wxss */
.math-formula-container {
  display: block;
  width: 100%;
  overflow-x: auto;
  padding: 5rpx 0;
  text-align: left;
}

.loading {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  padding: 10rpx;
}

.error-message {
  font-size: 28rpx;
  color: #e64340;
  text-align: center;
  padding: 10rpx;
}

.empty-formula {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  padding: 20rpx;
  border: 1px dashed #ddd;
  border-radius: 8rpx;
  margin: 10rpx 0;
}

.formula-image {
  max-width: 100%;
  margin: 10rpx auto;
  background-color: #fff;
  padding: 10rpx;
  border-radius: 8rpx;
}

.application-problem {
  font-size: 30rpx;
  color: #333;
  text-align: left;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  margin: 10rpx 0;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

/* towxml 样式覆盖 */
.math-formula-container .h2w {
  text-align: left !important;
  font-size: 30rpx !important;
  line-height: 1.6 !important;
}

.math-formula-container .h2w__p {
  margin: 10rpx 0 !important;
  text-align: left !important;
}

.math-formula-container .h2w__latex {
  display: inline-block !important;
  margin: 5rpx !important;
}

/* 结构化题目样式 */
.structured-problem {
  padding: 20rpx;
}

.problem-text {
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.options-container {
  margin-top: 20rpx;
}

.option-item {
  display: flex;
  align-items: center;
  margin: 15rpx 0;
  padding: 10rpx 0;
}

.option-label {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-right: 10rpx;
  min-width: 40rpx;
}

.formula-image-inline {
  max-width: 200rpx;
  max-height: 60rpx;
  margin: 0 10rpx;
}

.option-text {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}

.simple-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}
