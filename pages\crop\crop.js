// pages/crop/crop.js
Page({
  data: {
    imagePath: '',
    cropperOpt: {
      id: 'cropper',
      width: 300,
      height: 300,
      scale: 2.5,
      zoom: 8,
      cut: {
        x: 0,
        y: 0,
        width: 300,
        height: 300
      }
    }
  },

  onLoad: function (options) {
    if (options.imagePath) {
      const imagePath = decodeURIComponent(options.imagePath);
      this.setData({
        imagePath: imagePath
      });
      
      // 获取设备信息，调整裁剪区域大小
      wx.getSystemInfo({
        success: (res) => {
          const windowWidth = res.windowWidth;
          const windowHeight = res.windowHeight - 100; // 减去底部按钮区域高度
          
          // 计算裁剪区域大小，保持宽高比例为4:3（适合数学公式）
          let cropWidth = windowWidth * 0.9;
          let cropHeight = cropWidth * 0.75;
          
          // 确保裁剪区域不超过屏幕高度
          if (cropHeight > windowHeight * 0.8) {
            cropHeight = windowHeight * 0.8;
            cropWidth = cropHeight / 0.75;
          }
          
          // 计算裁剪区域位置，居中显示
          const x = (windowWidth - cropWidth) / 2;
          const y = (windowHeight - cropHeight) / 2;
          
          this.setData({
            'cropperOpt.width': windowWidth,
            'cropperOpt.height': windowHeight,
            'cropperOpt.cut.x': x,
            'cropperOpt.cut.y': y,
            'cropperOpt.cut.width': cropWidth,
            'cropperOpt.cut.height': cropHeight
          });
          
          // 初始化裁剪组件
          this.cropper = this.selectComponent('#image-cropper');
          this.cropper.imgReset();
        }
      });
    }
  },

  cropImage: function () {
    this.cropper.getImg((res) => {
      // 获取裁剪后的图片临时路径
      const tempFilePath = res.url;
      
      // 将裁剪结果返回给上一页面
      const eventChannel = this.getOpenerEventChannel();
      eventChannel.emit('cropResult', { tempFilePath: tempFilePath });
      
      // 返回上一页
      wx.navigateBack();
    });
  },

  cancel: function () {
    wx.navigateBack();
  },

  loadImage: function (e) {
    console.log('图片加载完成', e.detail);
  },

  imageLoad: function (e) {
    console.log('图片加载完成', e.detail);
    this.cropper.imgReset();
  }
})
