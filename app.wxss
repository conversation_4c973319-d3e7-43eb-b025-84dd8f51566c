/**app.wxss**/
/* 引入 towxml 样式 */
@import '/towxml/style/main.wxss';
@import '/towxml/style/theme/light.wxss';
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  background-color: #f8f8f8;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  padding: 30rpx;
}

.btn-primary {
  background-color: #4A90E2;
  color: white;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  margin: 20rpx 0;
  width: 80%;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  margin: 20rpx 0;
  width: 80%;
}

.card {
  background-color: #fff;
  border-radius: 15rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  width: 90%;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
  text-align: center;
}

.subtitle {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 30rpx;
  text-align: center;
}

.text-center {
  text-align: center;
}

.text-primary {
  color: #4A90E2;
}

.text-success {
  color: #52c41a;
}

.text-warning {
  color: #faad14;
}

.text-danger {
  color: #f5222d;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

.space-between {
  justify-content: space-between;
}

.mt-10 {
  margin-top: 10rpx;
}

.mt-20 {
  margin-top: 20rpx;
}

.mb-10 {
  margin-bottom: 10rpx;
}

.mb-20 {
  margin-bottom: 20rpx;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200rpx;
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* Animation for thinking process */
@keyframes thinking {
  0% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.2); opacity: 1; }
  100% { transform: scale(1); opacity: 0.7; }
}

.thinking-animation {
  width: 20rpx;
  height: 20rpx;
  background-color: #4A90E2;
  border-radius: 50%;
  margin: 0 10rpx;
  display: inline-block;
  animation: thinking 1.5s infinite;
}

.thinking-animation:nth-child(2) {
  animation-delay: 0.2s;
}

.thinking-animation:nth-child(3) {
  animation-delay: 0.4s;
}
