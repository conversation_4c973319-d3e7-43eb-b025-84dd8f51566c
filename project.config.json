{"description": "数学学习助手 - 帮助小学、初中、高中生学习数学的微信小程序", "packOptions": {"ignore": [], "include": []}, "miniprogramRoot": "", "cloudfunctionRoot": "cloudfunctions/", "compileType": "miniprogram", "projectname": "数学学习助手", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "showES6CompileOption": false, "compileWorklet": false, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "appid": "wx6c583e155f923210", "condition": {}, "simulatorPluginLibVersion": {}, "editorSetting": {}, "libVersion": "2.27.3"}