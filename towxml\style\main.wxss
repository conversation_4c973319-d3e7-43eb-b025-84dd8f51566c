/*正文样式*/
.h2w {
    font-family: PingFang SC, Lantinghei SC, Microsoft Yahei, Hiragino Sans GB, Microsoft Sans Serif, WenQuanYi Micro Hei, sans-serif;
    font-weight:300;
    font-size: 32rpx;
    line-height: 1.8;
    word-wrap: break-word;
    word-break: normal;
    text-align:justify;
}

.h2w__main {
    margin: 0 40rpx 40rpx 40rpx;
    padding-top: 40rpx;
}

/**标题**/
.h2w__h1,
.h2w__h2,
.h2w__h3,
.h2w__h4,
.h2w__h5,
.h2w__h6 {
    font-weight: bold;
}

/**设置行间元素样式**/
.h2w__span,
.h2w__b,
.h2w__strong,
.h2w__i,
.h2w__em,
.h2w__code,
.h2w__sub,
.h2w__sup,
.h2w__g-emoji,
.h2w__mark,
.h2w__u,
.h2w__navigator<PERSON>arent,
.h2w__ins {
    display:inline;
}

.h2w__h1 {
    border-bottom-style: double;
    border-bottom-width: 6rpx;
    font-size: 42rpx;
    padding-bottom: 10rpx;
    margin-bottom: 20rpx;
}

.h2w__h2 {
    border-bottom-style: solid;
    border-bottom-width: 1rpx;
    font-size: 40rpx;
    padding-bottom: 8rpx;
    margin-bottom: 18rpx;
}

.h2w__h3 {
    font-size: 38rpx;
    padding-bottom: 6rpx;
    margin-bottom: 12rpx;
}

.h2w__h4 {
    font-size: 36rpx;
    padding-bottom: 4rpx;
    margin-bottom: 12rpx;
}

.h2w__h5 {
    font-size: 34rpx;
    padding-bottom: 2rpx;
    margin-bottom: 12rpx;
}

.h2w__h6 {
    margin-bottom: 12rpx;
}

/**组件父级容器**/
.h2w__textParent, .h2w__viewParent {
    display:inline;
}
.h2w__rich-textParent {
    overflow-x:auto;
}

/**表格**/
.h2w__tableParent {
    width:100%;
    overflow-x:auto;
}

.h2w__table {
    width: 99.99%;
    border-collapse: collapse;
    border-spacing: 0;
    display: table;
    margin-bottom: 40rpx;
    white-space: nowrap;
}

.h2w__table .h2w__tr:nth-child(2n) {
    background-color: red;
}

.h2w__colgroup {
    display: table-column-group;
}

.h2w__col {
    display: table-column;
}

.h2w__thead {
    display: table-header-group;
}

.h2w__tbody {
    display: table-row-group;
}

.h2w__tfoot {
    display: table-footer-group;
}

.h2w__tr {
    display: table-row;
}

.h2w__th,
.h2w__td {
    padding: 8rpx 16rpx;
    font-size: 28rpx;
    border-width: 1rpx;
    border-style: solid;
    display: table-cell;
}

.h2w__th {
    font-weight: bold;
}

/**代码块**/
.h2w__pre {
    /*white-space:nowrap;*/
    padding: 10rpx 14rpx 10rpx 10rpx;
    font-size: 28rpx;
    word-break: normal;
    border-width: 1rpx;
    border-style: solid;
    margin-bottom: 40rpx;
    white-space: nowrap;
    overflow-x: auto;
    tab-size:4;
}
.h2w__pre .h2w__p {
    margin:0;
}

.h2w__pre .h2w__code {
    padding: 0;
    border: 0;
    font-size: 100%;
}

.h2w__pre,
.h2w__code {
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace, "STHeitiTC-Light", "Microsoft YaHei Light", -apple-system, system-ui, BlinkMacSystemFont;
}

.h2w__code {
    padding: 4rpx 8rpx;
    margin: 0 4rpx;
    border-width: 1rpx;
    border-style: solid;
    border-radius: 8rpx;
    font-size: 80%;
    overflow-x: auto;
}

.h2w__pre .h2w__span,
.h2w__pre .h2w__a,
.h2w__pre .h2w__span,
.h2w__pre .h2w__b,
.h2w__pre .h2w__strong,
.h2w__pre .h2w__i,
.h2w__pre .h2w__em {
    display: inline;
}

.h2w__pre {
    white-space: pre;
    display: block;
}

.h2w__pre .h2w__code {
    white-space:nowrap;
    /* width: 9999px; */
    display: block;
    font-size: 80%;
}

/**列表**/
.h2w__ul,
.h2w__ol {
    margin-bottom: 40rpx;
    padding-left: 1rem;
}

.h2w__ul .h2w__ol,
.h2w__ol .h2w__ul {
    margin-bottom: 0;
}

.h2w__li {
    display: list-item;
}

/**todo**/
.h2w__todogroup {
    margin-bottom: 40rpx;
}

.h2w__todogroup .h2w__todogroup {
    padding-left: 1.6rem;
}

/**一级ol样式**/
.h2w__ol {
    list-style-type: decimal;
}

/**二级ol样式**/
.h2w__ul .h2w__ol,
.h2w__ol .h2w__ol {
    list-style-type: lower-roman;
}

/**三级ol样式**/
.h2w__ul .h2w__ul .h2w__ol,
.h2w__ul .h2w__ol .h2w__ol,
.h2w__ol .h2w__ul .h2w__ol,
.h2w__ol .h2w__ol .h2w__ol {
    list-style-type: lower-alpha;
}

/**一级ul样式**/
.h2w__ul {
    list-style-type: disc;
}

/**二级ul样式**/
.h2w__ul .h2w__ul,
.h2w__ol .h2w__ul {
    list-style-type: circle;
}

/**三级样式**/
.h2w__ol .h2w__ol .h2w__ul,
.h2w__ol .h2w__ul .h2w__ul,
.h2w__ul .h2w__ol .h2w__ul,
.h2w__ul .h2w__ul .h2w__ul {
    list-style-type: square;
}

/**块元素**/
.h2w__p {
    margin: 20rpx 0 20rpx 0;
}

.h2w__blockquote {
    border-left-width: 8rpx;
    border-left-style: solid;
    padding: 0 20rpx;
}

/**内连元素**/
.h2w__a,
.h2w__span,
.h2w__s,
.h2w__b,
.h2w__strong,
.h2w__i,
.h2w__em {
    display: inline;
}

.h2w__b,
.h2w__strong {
    font-weight: bold;
}

.h2w__i,
.h2w__em {
    font-style: italic;
}

/**文本删除线**/
.h2w__s,
.h2w__strike,
.h2w__del {
    text-decoration: line-through;
}

/**文本下划线**/
.h2w__ins,
.h2w__u {
    text-decoration: underline;
}

/**链接**/
.h2w__a {
    margin: 0 8rpx;
    border-bottom-width: 1rpx;
    border-bottom-style: solid;
    line-height: 1;
}

.h2w__hr {
    height: 8rpx;
    margin: 40rpx 0;
}

/**荧光标记**/
.h2w__mark {
    border-radius: 4rpx;
}

/**上标、下标**/
.h2w__sup,
.h2w__sub {
    font-size: 75%;
    position: relative;
}

.h2w__sup {
    top: -0.5em;
}

.h2w__sub {
    bottom: -0.25em;
}

/**emoji表情**/
.h2w__g-emoji {
    margin: 0 0.1em;
    font-family: "Apple Color Emoji", "Segoe UI", "Segoe UI Emoji", "Segoe UI Symbol";
}

/**内置元素**/
image,video {
    max-width: 100%;
}


video {
    width:100%; margin: 10rpx auto;
}

image {
    height:auto; vertical-align:middle;
}

video {
    height:220px; font-size:0;
}

.h2w__latex--line {margin:4rpx 8rpx; vertical-align:middle;}
.h2w__latex--block {display:block; margin:1em auto;} 

.h2w__yuml {display:block;}

.h2w__yumlBox {
    width:100%;
    overflow-x:auto;
}
.h2w__yumlView {
    margin:0 auto; padding-bottom:40rpx;
}

/**代码行号**/
.h2w__lineNum {
    text-align:right; float:left; padding:0; margin:0 1em 0 0;
}
.h2w__lineNumLine {
    list-style:none;
}