/* pages/index/index.wxss */
.container {
  padding: 40rpx 30rpx;
}

.logo-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 30rpx;
}

.logo {
  width: 200rpx;
  height: 200rpx;
}

.slogan {
  width: 100%;
  text-align: center;
  margin-bottom: 40rpx;
}

.slogan-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #4A90E2;
}

.intro-card {
  width: 90%;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.intro-text {
  font-size: 32rpx;
  color: #666;
  line-height: 1.6;
  text-align: center;
}

.usage-info {
  width: 90%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.usage-text {
  font-size: 32rpx;
  margin-bottom: 10rpx;
}

.usage-subtext {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.action-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 60rpx;
}

.start-btn {
  width: 80%;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 36rpx;
  font-weight: bold;
}

.features-container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.feature-item {
  width: 48%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
  background-color: #fff;
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.feature-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
}
