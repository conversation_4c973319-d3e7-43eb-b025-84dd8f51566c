<!--pages/user/user.wxml-->
<view class="container">
  <view class="user-header">
    <view class="user-info" wx:if="{{isLoggedIn}}">
      <image class="user-avatar" src="/images/avatar.png" mode="aspectFit"></image>
      <view class="user-details">
        <text class="user-name">{{userInfo.name}}</text>
        <text class="user-grade">{{userInfo.grade}} | {{userInfo.textbook}}</text>
        <view class="user-badge {{isPremiumUser ? 'premium' : ''}}">
          <text class="badge-text">{{isPremiumUser ? '会员用户' : '免费用户'}}</text>
        </view>
      </view>
    </view>
    
    <view class="login-prompt" wx:else>
      <image class="login-avatar" src="/images/avatar_placeholder.png" mode="aspectFit"></image>
      <text class="login-text">请登录以使用完整功能</text>
      <button class="btn-primary login-btn" bindtap="goToLogin">立即登录</button>
    </view>
  </view>
  
  <view class="usage-card" wx:if="{{isLoggedIn && !isPremiumUser}}">
    <view class="usage-header">
      <text class="usage-title">使用情况</text>
      <text class="usage-count">{{usageCount}}/{{maxFreeUsage}}</text>
    </view>
    <progress class="usage-progress" percent="{{usageCount / maxFreeUsage * 100}}" stroke-width="4" activeColor="#4A90E2" backgroundColor="#e0e0e0"></progress>
    <button class="btn-primary upgrade-btn" bindtap="goToPayment">升级为会员</button>
  </view>
  
  <view class="menu-container">
    <view class="menu-group">
      <view class="menu-title">账户管理</view>
      <view class="menu-item" bindtap="goToPayment" wx:if="{{isLoggedIn && !isPremiumUser}}">
        <image class="menu-icon" src="/images/vip.png" mode="aspectFit"></image>
        <text class="menu-text">会员升级</text>
        <image class="arrow-icon" src="/images/arrow.png" mode="aspectFit"></image>
      </view>
      <view class="menu-item" wx:if="{{isPremiumUser}}">
        <image class="menu-icon" src="/images/vip.png" mode="aspectFit"></image>
        <text class="menu-text">会员状态</text>
        <text class="menu-value">已激活</text>
      </view>
      <view class="menu-item" bindtap="logout" wx:if="{{isLoggedIn}}">
        <image class="menu-icon" src="/images/logout.png" mode="aspectFit"></image>
        <text class="menu-text">退出登录</text>
        <image class="arrow-icon" src="/images/arrow.png" mode="aspectFit"></image>
      </view>
    </view>
    
    <view class="menu-group">
      <view class="menu-title">帮助与支持</view>
      <view class="menu-item" bindtap="contactSupport">
        <image class="menu-icon" src="/images/support.png" mode="aspectFit"></image>
        <text class="menu-text">联系客服</text>
        <image class="arrow-icon" src="/images/arrow.png" mode="aspectFit"></image>
      </view>
      <view class="menu-item" bindtap="showAbout">
        <image class="menu-icon" src="/images/about.png" mode="aspectFit"></image>
        <text class="menu-text">关于我们</text>
        <image class="arrow-icon" src="/images/arrow.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>
  
  <view class="app-version">
    <text class="version-text">数学学习助手 v1.0.0</text>
  </view>
</view>
