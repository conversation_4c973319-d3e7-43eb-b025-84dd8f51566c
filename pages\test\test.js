// pages/test/test.js
const towxml = require('../../towxml/index');

Page({
  data: {
    article: {},
    formula: '(1-\\sqrt{2})^2+4\\sqrt{\\frac{1}{2}+\\frac{1}{1+\\sqrt{2}}}-\\frac{1}{2}\\sqrt{8}'
  },

  onLoad: function (options) {
    this.renderFormula();
  },

  renderFormula: function() {
    try {
      // 将公式包装在 Markdown 中，使用 $$ 包裹 LaTeX 公式
      const markdownWithLatex = `$$${this.data.formula}$$`;
      
      // 使用 towxml 解析 Markdown
      const article = towxml(markdownWithLatex, 'markdown', {
        theme: 'light',
        events: {
          tap: (e) => {
            console.log('tap', e);
          }
        }
      });
      
      this.setData({
        article: article
      });
    } catch (error) {
      console.error('公式渲染错误:', error);
      wx.showToast({
        title: '公式渲染失败',
        icon: 'none'
      });
    }
  }
})
