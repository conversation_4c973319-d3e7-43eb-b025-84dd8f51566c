// pages/test/test.js
const towxml = require('../../towxml/index');

Page({
  data: {
    article: {},
    formula: '例1 某轮船的静水速度为v千米/时，水流速度为m千米/时，则这艘轮船在两码头间往返一次顺流与逆流的时间比是（）\n\nA. $$\\frac{v+m}{v-m}$$\n\nB. $$\\frac{v-m}{v+m}$$\n\nC. $$\\frac{1}{v} + \\frac{1}{m}$$\n\nD. $$\\frac{v}{m}$$'
  },

  onLoad: function (options) {
    this.renderFormula();
  },

  renderFormula: function() {
    try {
      console.log('测试页面渲染公式:', this.data.formula);

      // 使用 towxml 解析 Markdown
      const article = towxml(this.data.formula, 'markdown', {
        theme: 'light',
        events: {
          tap: (e) => {
            console.log('tap', e);
          }
        }
      });

      console.log('towxml解析结果:', article);

      this.setData({
        article: article
      });
    } catch (error) {
      console.error('公式渲染错误:', error);
      wx.showToast({
        title: '公式渲染失败',
        icon: 'none'
      });
    }
  }
})
