# This is a basic workflow to help you get started with Actions

name: Build

# Controls when the action will run. 
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2

      # Runs a single command using the runners shell
      - name: 编译文件
        run: |
          yarn
          yarn run build
          
      # Runs a set of commands using the runners shell
      - name: Update dist folder to repository
        env:
          # 仓库用户名
          REPO_USERNAME: ""
          # GitHub Pages仓库名
          DIST_REPO: ""
          # GitHub Token
          GITHUB_TOKEN: ${{ secrets.G_TOKEN }}

        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitHub Action"

          cd dist && git init && git add .
          git commit -m "GitHub Actions Auto Builder at $(date +'%Y-%m-%d %H:%M:%S')"
          git push --force --quiet "https://$<EMAIL>/$REPO_USERNAME/$DIST_REPO.git" master:master
