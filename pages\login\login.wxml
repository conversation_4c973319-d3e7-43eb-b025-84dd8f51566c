<!--pages/login/login.wxml-->
<view class="container">
  <view class="header">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <text class="title">欢迎使用数学学习助手</text>
    <text class="subtitle">为了精确辅导，请提供以下信息</text>
  </view>
  
  <view class="form-container">
    <view class="form-item">
      <text class="form-label">学生姓名</text>
      <input class="form-input" placeholder="请输入学生姓名" bindinput="inputStudentName" value="{{studentName}}"/>
    </view>
    
    <view class="form-item">
      <text class="form-label">所在地域</text>
      <picker bindchange="bindRegionChange" value="{{regionIndex}}" range="{{regions}}">
        <view class="picker">
          {{regions[regionIndex]}}
        </view>
      </picker>
    </view>
    
    <view class="form-item">
      <text class="form-label">年级</text>
      <picker bindchange="bindGradeChange" value="{{gradeIndex}}" range="{{grades}}">
        <view class="picker">
          {{grades[gradeIndex]}}
        </view>
      </picker>
    </view>
    
    <view class="form-item">
      <text class="form-label">教材版本</text>
      <picker bindchange="bindTextbookChange" value="{{textbookIndex}}" range="{{textbooks}}">
        <view class="picker">
          {{textbooks[textbookIndex]}}
        </view>
      </picker>
    </view>
    
    <button class="btn-primary submit-btn" bindtap="submitForm" disabled="{{isSubmitting}}">
      {{isSubmitting ? '提交中...' : '开始学习'}}
    </button>
    
    <view class="privacy-notice">
      <text class="privacy-text">提交即表示您同意我们的</text>
      <text class="privacy-link">用户协议</text>
      <text class="privacy-text">和</text>
      <text class="privacy-link">隐私政策</text>
    </view>
  </view>
</view>
