/* pages/solver/solver.wxss */
.container {
  padding: 30rpx;
  height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.step-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.step-title {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* Upload Step */
.upload-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-btn {
  width: 300rpx;
  height: 300rpx;
  border-radius: 20rpx;
  background-color: #f0f0f0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2rpx dashed #ccc;
}

.upload-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
}

.upload-text {
  font-size: 28rpx;
  color: #666;
}

/* Confirm Step */
.problem-image-container {
  width: 100%;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: center;
}

.problem-image {
  width: 90%;
  max-height: 400rpx;
  border-radius: 10rpx;
}

.problem-text-container {
  width: 100%;
  margin-bottom: 30rpx;
}

.problem-text {
  width: 100%;
  height: 200rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 30rpx;
  background-color: #f9f9f9;
}

.formula-preview-container {
  width: 100%;
  margin: 20rpx 0;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.preview-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.formula-preview {
  padding: 15rpx;
  border: 1rpx dashed #e0e0e0;
  border-radius: 8rpx;
  background-color: #fff;
  min-height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow-x: auto;
}

.formula-preview:active {
  background-color: #f5f5f5;
}

.edit-hint {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-top: 10rpx;
  font-style: italic;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.action-buttons button {
  width: 45%;
}

/* Solve Step */
.problem-display {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.problem-title {
  font-weight: bold;
  font-size: 30rpx;
  margin-bottom: 10rpx;
  display: block;
}

.problem-content {
  font-size: 28rpx;
  line-height: 1.6;
  padding: 15rpx;
  border: 1rpx dashed #e0e0e0;
  border-radius: 8rpx;
  background-color: #f9f9f9;
  margin-top: 10rpx;
}

.conversation-container {
  flex: 1;
  background-color: #f9f9f9;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.conversation-scroll {
  height: 100%;
  padding: 20rpx;
}

.message {
  margin-bottom: 20rpx;
  max-width: 80%;
}

.message.assistant {
  align-self: flex-start;
}

.message.user {
  align-self: flex-end;
  margin-left: auto;
}

.message-content {
  padding: 15rpx 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  line-height: 1.5;
  word-wrap: break-word;
}

.assistant .message-content {
  background-color: #e1f3ff;
  color: #333;
}

.user .message-content {
  background-color: #4A90E2;
  color: white;
}

.thinking-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20rpx 0;
}

.thinking-dots {
  display: flex;
  justify-content: center;
  margin-bottom: 10rpx;
}

.thinking-text {
  font-size: 24rpx;
  color: #999;
}

.input-container {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.answer-input {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.send-btn {
  width: 120rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  padding: 0;
  margin: 0 0 0 10rpx;
  background-color: #4A90E2;
  color: white;
}

.helper-buttons {
  display: flex;
  justify-content: space-between;
}

.helper-btn {
  width: 30%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  padding: 0;
  background-color: #f0f0f0;
  color: #333;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  max-height: 80%;
  background-color: #fff;
  border-radius: 15rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #e0e0e0;
}

.modal-title {
  font-size: 34rpx;
  font-weight: bold;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}

.modal-body {
  padding: 30rpx;
  overflow-y: auto;
  flex: 1;
}

.modal-text {
  font-size: 28rpx;
  line-height: 1.6;
}
