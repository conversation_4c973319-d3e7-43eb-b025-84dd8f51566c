/**
 * 简化版KaTeX渲染器，专为微信小程序设计
 * 支持基本的LaTeX数学公式渲染
 */

// 支持的数学符号映射
const SYMBOLS = {
  // 希腊字母
  '\\alpha': 'α',
  '\\beta': 'β',
  '\\gamma': 'γ',
  '\\delta': 'δ',
  '\\epsilon': 'ε',
  '\\zeta': 'ζ',
  '\\eta': 'η',
  '\\theta': 'θ',
  '\\iota': 'ι',
  '\\kappa': 'κ',
  '\\lambda': 'λ',
  '\\mu': 'μ',
  '\\nu': 'ν',
  '\\xi': 'ξ',
  '\\pi': 'π',
  '\\rho': 'ρ',
  '\\sigma': 'σ',
  '\\tau': 'τ',
  '\\upsilon': 'υ',
  '\\phi': 'φ',
  '\\chi': 'χ',
  '\\psi': 'ψ',
  '\\omega': 'ω',
  
  // 运算符
  '\\times': '×',
  '\\div': '÷',
  '\\pm': '±',
  '\\mp': '∓',
  '\\cdot': '·',
  '\\bullet': '•',
  
  // 关系符
  '\\ne': '≠',
  '\\neq': '≠',
  '\\ge': '≥',
  '\\geq': '≥',
  '\\le': '≤',
  '\\leq': '≤',
  '\\approx': '≈',
  '\\equiv': '≡',
  
  // 其他符号
  '\\infty': '∞',
  '\\partial': '∂',
  '\\nabla': '∇',
  '\\exists': '∃',
  '\\forall': '∀',
  '\\in': '∈',
  '\\notin': '∉',
  '\\subset': '⊂',
  '\\supset': '⊃',
  '\\cup': '∪',
  '\\cap': '∩',
};

// 预处理LaTeX公式
function preprocess(latex) {
  if (!latex) return '';
  
  // 移除公式环境标记
  latex = latex.replace(/^\\\[|\\\]$/g, '');
  latex = latex.replace(/^\\\(|\\\)$/g, '');
  latex = latex.replace(/^\$|\$$/g, '');
  
  // 处理特殊情况
  latex = preprocessFractions(latex);
  latex = preprocessSqrt(latex);
  latex = preprocessBrackets(latex);
  
  return latex;
}

// 预处理分数
function preprocessFractions(latex) {
  // 处理 \frac{...}{...} 中可能嵌套的情况
  let result = latex;
  
  // 处理没有括号的 \frac 后跟两个字符的情况，如 \frac12
  result = result.replace(/\\frac([^{])([^{])/g, '\\frac{$1}{$2}');
  
  return result;
}

// 预处理根号
function preprocessSqrt(latex) {
  let result = latex;
  
  // 处理 \sqrt 后没有花括号的情况
  result = result.replace(/\\sqrt(\d+)/g, '\\sqrt{$1}');
  result = result.replace(/\\sqrt([a-zA-Z])/g, '\\sqrt{$1}');
  
  // 处理 \sqrt 后有空格再跟数字或字母的情况
  result = result.replace(/\\sqrt\s+(\d+)/g, '\\sqrt{$1}');
  result = result.replace(/\\sqrt\s+([a-zA-Z])/g, '\\sqrt{$1}');
  
  return result;
}

// 预处理括号
function preprocessBrackets(latex) {
  let result = latex;
  
  // 处理 \left 和 \right
  result = result.replace(/\\left\(/g, '(');
  result = result.replace(/\\right\)/g, ')');
  result = result.replace(/\\left\[/g, '[');
  result = result.replace(/\\right\]/g, ']');
  result = result.replace(/\\left\\{/g, '{');
  result = result.replace(/\\right\\}/g, '}');
  
  return result;
}

// 解析LaTeX公式并转换为HTML
function parseLatex(latex) {
  // 预处理
  latex = preprocess(latex);
  
  // 解析各种数学结构
  let html = latex;
  
  // 处理分数
  html = parseFractions(html);
  
  // 处理根号
  html = parseSqrt(html);
  
  // 处理上下标
  html = parseSupAndSub(html);
  
  // 处理数学符号
  html = parseSymbols(html);
  
  // 处理括号
  html = parseBrackets(html);
  
  // 移除多余的反斜杠
  html = html.replace(/\\/g, '');
  
  return html;
}

// 解析分数
function parseFractions(latex) {
  // 处理复杂分数，如 \frac{1+\sqrt{2}}{2}
  let result = latex.replace(/\\frac\{([^{}]+(?:\{[^{}]*\}[^{}]*)*)\}\{([^{}]+(?:\{[^{}]*\}[^{}]*)*)\}/g, 
    '<span class="katex-frac"><span class="katex-num">$1</span><span class="katex-denom">$2</span></span>');
  
  // 处理简单分数，如 \frac{1}{2}
  result = result.replace(/\\frac\{([^{}]+)\}\{([^{}]+)\}/g, 
    '<span class="katex-frac"><span class="katex-num">$1</span><span class="katex-denom">$2</span></span>');
  
  return result;
}

// 解析根号
function parseSqrt(latex) {
  // 处理根号，如 \sqrt{2}
  return latex.replace(/\\sqrt\{([^{}]+(?:\{[^{}]*\}[^{}]*)*)\}/g, 
    '<span class="katex-sqrt"><span class="katex-sqrt-sym">√</span><span class="katex-sqrt-arg">$1</span></span>');
}

// 解析上下标
function parseSupAndSub(latex) {
  let result = latex;
  
  // 处理上标 ^{...} 或 ^x
  result = result.replace(/\^(?:\{([^{}]+(?:\{[^{}]*\}[^{}]*)*)\}|([^{}\s]))/g, 
    '<span class="katex-sup">$1$2</span>');
  
  // 处理下标 _{...} 或 _x
  result = result.replace(/_(?:\{([^{}]+(?:\{[^{}]*\}[^{}]*)*)\}|([^{}\s]))/g, 
    '<span class="katex-sub">$1$2</span>');
  
  return result;
}

// 解析数学符号
function parseSymbols(latex) {
  let result = latex;
  
  // 替换所有已知符号
  for (const [symbol, replacement] of Object.entries(SYMBOLS)) {
    const regex = new RegExp(symbol.replace(/\\/g, '\\\\'), 'g');
    result = result.replace(regex, replacement);
  }
  
  return result;
}

// 解析括号
function parseBrackets(latex) {
  return latex;
}

// 导出函数
module.exports = {
  render: parseLatex
};
