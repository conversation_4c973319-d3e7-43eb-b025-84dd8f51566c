/* pages/user/user.wxss */
.container {
  padding: 0;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.user-header {
  background-color: #4A90E2;
  padding: 40rpx 30rpx;
  color: white;
  margin-bottom: 30rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: white;
  margin-right: 30rpx;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.user-grade {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 15rpx;
  display: block;
}

.user-badge {
  display: inline-block;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  padding: 6rpx 20rpx;
}

.user-badge.premium {
  background-color: #FFD700;
}

.badge-text {
  font-size: 24rpx;
  color: #333;
}

.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
}

.login-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: white;
  margin-bottom: 20rpx;
}

.login-text {
  font-size: 32rpx;
  margin-bottom: 30rpx;
}

.login-btn {
  width: 60%;
}

.usage-card {
  margin: 0 30rpx 30rpx;
  background-color: white;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.usage-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.usage-count {
  font-size: 32rpx;
  color: #4A90E2;
  font-weight: bold;
}

.usage-progress {
  margin-bottom: 30rpx;
}

.upgrade-btn {
  width: 100%;
}

.menu-container {
  margin: 0 30rpx;
}

.menu-group {
  background-color: white;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.menu-title {
  font-size: 28rpx;
  color: #999;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-value {
  font-size: 28rpx;
  color: #999;
  margin-right: 10rpx;
}

.arrow-icon {
  width: 30rpx;
  height: 30rpx;
}

.app-version {
  text-align: center;
  padding: 40rpx 0;
}

.version-text {
  font-size: 26rpx;
  color: #999;
}
