// pages/user/user.js
const app = getApp()

Page({
  data: {
    userInfo: null,
    isLoggedIn: false,
    isPremiumUser: false,
    usageCount: 0,
    maxFreeUsage: 5
  },

  onLoad: function (options) {
    this.updateUserInfo();
  },

  onShow: function () {
    this.updateUserInfo();
  },

  updateUserInfo: function () {
    const isLoggedIn = app.globalData.isLoggedIn;
    const userInfo = app.globalData.userInfo;
    const isPremiumUser = app.globalData.isPremiumUser;
    const usageCount = app.globalData.usageCount;
    const maxFreeUsage = app.globalData.maxFreeUsage;
    
    this.setData({
      isLoggedIn,
      userInfo,
      isPremiumUser,
      usageCount,
      maxFreeUsage
    });
  },

  goToLogin: function () {
    wx.navigateTo({
      url: '/pages/login/login',
    });
  },

  goToPayment: function () {
    wx.navigateTo({
      url: '/pages/payment/payment',
    });
  },

  logout: function () {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // Clear user info
          wx.removeStorageSync('userInfo');
          app.globalData.userInfo = null;
          app.globalData.isLoggedIn = false;
          
          // Update UI
          this.setData({
            isLoggedIn: false,
            userInfo: null
          });
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  contactSupport: function () {
    wx.showModal({
      title: '联系客服',
      content: '如有任何问题，请联系我们的客服团队：\n\n电话：400-123-4567\n邮箱：<EMAIL>',
      showCancel: false
    });
  },

  showAbout: function () {
    wx.showModal({
      title: '关于我们',
      content: '数学学习助手是一款专为小学、初中、高中学生设计的数学学习工具，利用人工智能技术帮助学生一步步解决数学问题，提高学习效率。\n\n版本：1.0.0',
      showCancel: false
    });
  }
})
